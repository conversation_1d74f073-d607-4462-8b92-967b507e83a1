// Standardized API Response Format
export type SuccessResponse<T> = {
  success: true;
  data: T;
  timestamp: string;
}

export type ErrorResponse = {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse;

// Legacy response type for backward compatibility
export type LegacyApiResponse = {
  message: string;
  success: true;
}

export type User = {
  id: string;
  name: string;
  color: string;
  createdAt?: string;
}

export type BucketCollaborator = {
  bucketId: string;
  userId: string;
  role: 'owner' | 'member';
  joinedAt?: string;
}

export type PurchaseOption = {
  id: string;
  itemId: string;
  storeName: string;
  price?: number;
  link?: string;
  createdAt?: string;
}

export type ItemStatus = 'pending' | 'in-cart' | 'bought';

export type Item = {
  id: string;
  name: string;
  notes?: string;
  categoryId: string;
  purchaseOptions?: PurchaseOption[];
  status: ItemStatus;
  assignedUserId?: string;
  selectedOptionId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export type Category = {
  id: string;
  name: string;
  bucketId: string;
  items?: Item[];
  createdAt?: string;
  updatedAt?: string;
}

export type Bucket = {
  id: string;
  name: string;
  emoji: string;
  ownerId: string;
  categories?: Category[];
  collaborators?: BucketCollaborator[];
  budget?: number;
  createdAt?: string;
  updatedAt?: string;
}

export type Application = {
  id: string;
  name: string;
  icon: string;
  isActive: boolean;
}
