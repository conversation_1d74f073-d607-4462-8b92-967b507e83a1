"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { Bucket, Category, Item, User, Application } from 'shared'

interface ShoppingContextType {
  // Data
  applications: Application[]
  buckets: Bucket[]
  users: User[]
  activeBucketId: string | null
  activeCategoryId: string | null
  
  // Computed values
  activeBucket: Bucket | null
  activeCategories: Category[]
  
  // Actions
  setActiveBucket: (bucketId: string) => void
  setActiveCategory: (categoryId: string) => void
  addBucket: (bucket: Omit<Bucket, 'id'>) => void
  updateBucket: (id: string, updates: Partial<Bucket>) => void
  deleteBucket: (id: string) => void
  addCategory: (category: Omit<Category, 'id'>) => void
  updateCategory: (id: string, updates: Partial<Category>) => void
  deleteCategory: (id: string) => void
  addItem: (item: Omit<Item, 'id'>) => void
  updateItem: (id: string, updates: Partial<Item>) => void
  deleteItem: (id: string) => void
}

const ShoppingContext = createContext<ShoppingContextType | null>(null)

const STORAGE_KEY = 'shopping-list-data'

// Default data
const defaultData = {
  applications: [
    {
      id: "shopping-list",
      name: "Shopping List",
      icon: "🛍️",
      isActive: true,
    },
    {
      id: "budget-planning",
      name: "Budget Planning",
      icon: "💰",
      isActive: false,
    },
    {
      id: "debt-management",
      name: "Debt Management",
      icon: "📊",
      isActive: false,
    },
  ] as Application[],
  buckets: [
    {
      id: "move-to-new-home",
      name: "Move to New Home",
      emoji: "🏠",
      ownerId: "user-1",
      categories: [
        {
          id: "furniture",
          name: "Furniture",
          bucketId: "move-to-new-home",
          items: [
            {
              id: "sofa",
              name: "Living Room Sofa",
              notes: "Need something comfortable for 4 people",
              categoryId: "furniture",
              purchaseOptions: [
                {
                  id: "ikea-sofa",
                  itemId: "sofa",
                  storeName: "IKEA",
                  price: 799,
                  link: "https://ikea.com/sofa",
                },
              ],
              status: "pending" as const,
            },
          ],
        },
        {
          id: "workspace",
          name: "Workspace",
          bucketId: "move-to-new-home",
          items: [],
        },
      ],
    },
    {
      id: "travel-to-dubai",
      name: "Travel to Dubai",
      emoji: "✈️",
      ownerId: "user-1",
      categories: [
        {
          id: "accommodations",
          name: "Accommodations",
          bucketId: "travel-to-dubai",
          items: [],
        },
        {
          id: "clothes",
          name: "Clothes",
          bucketId: "travel-to-dubai",
          items: [],
        },
      ],
    },
  ] as Bucket[],
  users: [
    {
      id: "user-1",
      name: "Tegar Imansyah",
      color: "#3b82f6",
    },
    {
      id: "user-2",
      name: "Wife",
      color: "#ec4899",
    },
  ] as User[],
}

export function ShoppingProvider({ children }: { children: React.ReactNode }) {
  const [applications, setApplications] = useState<Application[]>(defaultData.applications)
  const [buckets, setBuckets] = useState<Bucket[]>(defaultData.buckets)
  const [users, setUsers] = useState<User[]>(defaultData.users)
  const [activeBucketId, setActiveBucketId] = useState<string | null>(defaultData.buckets[0]?.id || null)
  const [activeCategoryId, setActiveCategoryId] = useState<string | null>(null)
  
  // Load data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem(STORAGE_KEY)
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData)
        setApplications(parsed.applications || defaultData.applications)
        setBuckets(parsed.buckets || defaultData.buckets)
        setUsers(parsed.users || defaultData.users)
        setActiveBucketId(parsed.activeBucketId || defaultData.buckets[0]?.id || null)
        setActiveCategoryId(parsed.activeCategoryId || null)
      } catch (error) {
        console.error('Failed to load data from localStorage:', error)
      }
    }
  }, [])
  
  // Save data to localStorage whenever state changes
  useEffect(() => {
    const dataToSave = {
      applications,
      buckets,
      users,
      activeBucketId,
      activeCategoryId,
    }
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave))
  }, [applications, buckets, users, activeBucketId, activeCategoryId])
  
  // Computed values
  const activeBucket = buckets.find(bucket => bucket.id === activeBucketId) || null
  const activeCategories = activeBucket?.categories || []
  
  // Helper function to generate unique IDs
  const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2)
  
  // Actions
  const setActiveBucket = (bucketId: string) => {
    setActiveBucketId(bucketId)
    setActiveCategoryId(null) // Reset active category when switching buckets
  }
  
  const setActiveCategory = (categoryId: string) => {
    setActiveCategoryId(categoryId)
  }
  
  const addBucket = (bucketData: Omit<Bucket, 'id'>) => {
    const newBucket: Bucket = {
      ...bucketData,
      id: generateId(),
    }
    setBuckets(prev => [...prev, newBucket])
    setActiveBucketId(newBucket.id)
  }
  
  const updateBucket = (id: string, updates: Partial<Bucket>) => {
    setBuckets(prev => prev.map(bucket => 
      bucket.id === id ? { ...bucket, ...updates } : bucket
    ))
  }
  
  const deleteBucket = (id: string) => {
    setBuckets(prev => prev.filter(bucket => bucket.id !== id))
    if (activeBucketId === id) {
      const remainingBuckets = buckets.filter(bucket => bucket.id !== id)
      setActiveBucketId(remainingBuckets[0]?.id || null)
    }
  }
  
  const addCategory = (categoryData: Omit<Category, 'id'>) => {
    const newCategory: Category = {
      ...categoryData,
      id: generateId(),
    }
    setBuckets(prev => prev.map(bucket => 
      bucket.id === categoryData.bucketId
        ? { ...bucket, categories: [...bucket.categories, newCategory] }
        : bucket
    ))
  }
  
  const updateCategory = (id: string, updates: Partial<Category>) => {
    setBuckets(prev => prev.map(bucket => ({
      ...bucket,
      categories: bucket.categories.map(category =>
        category.id === id ? { ...category, ...updates } : category
      )
    })))
  }
  
  const deleteCategory = (id: string) => {
    setBuckets(prev => prev.map(bucket => ({
      ...bucket,
      categories: bucket.categories.filter(category => category.id !== id)
    })))
    if (activeCategoryId === id) {
      setActiveCategoryId(null)
    }
  }
  
  const addItem = (itemData: Omit<Item, 'id'>) => {
    const newItem: Item = {
      ...itemData,
      id: generateId(),
    }
    setBuckets(prev => prev.map(bucket => ({
      ...bucket,
      categories: bucket.categories.map(category =>
        category.id === itemData.categoryId
          ? { ...category, items: [...category.items, newItem] }
          : category
      )
    })))
  }
  
  const updateItem = (id: string, updates: Partial<Item>) => {
    setBuckets(prev => prev.map(bucket => ({
      ...bucket,
      categories: bucket.categories.map(category => ({
        ...category,
        items: category.items.map(item =>
          item.id === id ? { ...item, ...updates } : item
        )
      }))
    })))
  }
  
  const deleteItem = (id: string) => {
    setBuckets(prev => prev.map(bucket => ({
      ...bucket,
      categories: bucket.categories.map(category => ({
        ...category,
        items: category.items.filter(item => item.id !== id)
      }))
    })))
  }
  
  const value: ShoppingContextType = {
    // Data
    applications,
    buckets,
    users,
    activeBucketId,
    activeCategoryId,
    
    // Computed values
    activeBucket,
    activeCategories,
    
    // Actions
    setActiveBucket,
    setActiveCategory,
    addBucket,
    updateBucket,
    deleteBucket,
    addCategory,
    updateCategory,
    deleteCategory,
    addItem,
    updateItem,
    deleteItem,
  }
  
  return (
    <ShoppingContext.Provider value={value}>
      {children}
    </ShoppingContext.Provider>
  )
}

export function useShopping() {
  const context = useContext(ShoppingContext)
  if (!context) {
    throw new Error('useShopping must be used within a ShoppingProvider')
  }
  return context
}