import { Outlet } from "react-router";
import { SidebarLeft } from "@/components/sidebar-left";
import { SidebarRight } from "@/components/sidebar-right";
import { MobileTotalsBar } from "@/components/mobile-totals-bar";
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar";
import { ShoppingProvider, useShopping } from "@/contexts/shopping-context";

function RootLayoutContent() {
  const { activeCategories, activeBucket } = useShopping();

  return (
    <SidebarProvider>
      <SidebarLeft />
      <SidebarInset className="relative">
        <Outlet />
      </SidebarInset>
      <SidebarRight />
      <MobileTotalsBar
        categories={activeCategories}
        budget={activeBucket?.budget}
      />
    </SidebarProvider>
  );
}

export function RootLayout() {
  return (
    <ShoppingProvider>
      <RootLayoutContent />
    </ShoppingProvider>
  );
}
