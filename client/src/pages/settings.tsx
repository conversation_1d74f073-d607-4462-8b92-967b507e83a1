import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"

export function SettingsPage() {
  return (
    <>
      <header className="bg-background sticky top-0 flex h-14 shrink-0 items-center gap-2">
        <div className="flex flex-1 items-center gap-2 px-3">
          <SidebarTrigger />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <div className="flex items-center gap-2">
            <span className="text-xl">⚙️</span>
            <h1 className="font-semibold">Settings</h1>
          </div>
        </div>
      </header>
      <div className="flex flex-1 flex-col gap-8 p-6">
        <div className="flex items-center justify-center h-96">
          <div className="text-center text-muted-foreground">
            <div className="text-6xl mb-4">⚙️</div>
            <h2 className="text-xl font-semibold mb-2">Settings</h2>
            <p>User settings and preferences will be available here.</p>
          </div>
        </div>
      </div>
    </>
  )
}
