import * as React from "react"
import { useParams, useNavigate, Outlet } from "react-router"
import { CategorySection } from "@/components/category-section"
import { ItemModal } from "@/components/item-modal"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { useShopping } from "@/contexts/shopping-context"
import { Item, ItemStatus } from "shared"
import { Grid3X3, List, Plus, Check } from "lucide-react"

export function ShoppingListPage() {
    const { bucketId, categoryId } = useParams()
    const navigate = useNavigate()
    const {
        activeBucket,
        activeCategories,
        activeCategoryId,
        users,
        addItem,
        updateItem,
        deleteItem,
        addCategory,
        setActiveBucket,
        setActiveCategory,
        buckets
    } = useShopping()

    const [itemModalOpen, setItemModalOpen] = React.useState(false)
    const [itemModalCategoryId, setItemModalCategoryId] = React.useState<string>("")
    const [editingItem, setEditingItem] = React.useState<Item | null>(null)
    const [viewMode, setViewMode] = React.useState<'card' | 'simple'>('simple')
    const [newItemInputs, setNewItemInputs] = React.useState<{ [categoryId: string]: string }>({})

    // Sync URL params with application state
    React.useEffect(() => {
        if (bucketId && bucketId !== activeBucket?.id) {
            const bucket = buckets.find(b => b.id === bucketId)
            if (bucket) {
                setActiveBucket(bucketId)
            } else {
                // Bucket not found, redirect to main shopping list
                navigate('/shopping-list', { replace: true })
            }
        }
    }, [bucketId, activeBucket?.id, buckets, setActiveBucket, navigate])

    React.useEffect(() => {
        if (categoryId && categoryId !== activeCategoryId) {
            const category = activeCategories.find(c => c.id === categoryId)
            if (category) {
                setActiveCategory(categoryId)
            } else if (activeBucket) {
                // Category not found, redirect to bucket view
                navigate(`/shopping-list/bucket/${activeBucket.id}`, { replace: true })
            }
        }
    }, [categoryId, activeCategoryId, activeCategories, setActiveCategory, activeBucket, navigate])

    const handleAddItem = (categoryId: string) => {
        setItemModalCategoryId(categoryId)
        setEditingItem(null)
        setItemModalOpen(true)
    }

    const handleEditItem = (item: Item) => {
        setEditingItem(item)
        setItemModalCategoryId(item.categoryId)
        setItemModalOpen(true)
    }

    const handleDeleteItem = (itemId: string) => {
        deleteItem(itemId)
    }

    const handleStatusChange = (itemId: string, status: ItemStatus) => {
        updateItem(itemId, { status })
    }

    const handleAssignUser = (itemId: string, userId: string | undefined) => {
        updateItem(itemId, { assignedUserId: userId })
    }

    const handleSimpleAddItem = (categoryId: string, name: string) => {
        if (name.trim()) {
            addItem({
                name: name.trim(),
                categoryId,
                status: 'pending',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            })
            setNewItemInputs(prev => ({ ...prev, [categoryId]: '' }))
        }
    }

    const handleSimpleToggleItem = (item: Item) => {
        const newStatus: ItemStatus = item.status === 'bought' ? 'pending' : 'bought'
        updateItem(item.id, { status: newStatus })
    }

    // If we have nested routes (bucket/category), render the outlet
    if (bucketId || categoryId) {
        return <Outlet />
    }

    return (
        <>
            <header className="bg-background sticky top-0 flex h-14 shrink-0 items-center gap-2">
                <div className="flex flex-1 items-center gap-2 px-3">
                    <SidebarTrigger />
                    <Separator
                        orientation="vertical"
                        className="mr-2 data-[orientation=vertical]:h-4"
                    />
                    <div className="flex items-center gap-2">
                        <span className="text-xl">{activeBucket?.emoji || "🛍️"}</span>
                        <h1 className="font-semibold">{activeBucket?.name || "Shopping List"}</h1>
                    </div>
                </div>
                <div className="flex items-center gap-2 px-3">
                    {/* Large desktop view - both buttons with text */}
                    <div className="hidden xl:flex items-center gap-2">
                        <Button
                            variant={viewMode === 'card' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('card')}
                        >
                            <Grid3X3 className="mr-2 h-4 w-4" />
                            Card View
                        </Button>
                        <Button
                            variant={viewMode === 'simple' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('simple')}
                        >
                            <List className="mr-2 h-4 w-4" />
                            Simple View
                        </Button>
                    </div>

                    {/* Medium and mobile view - icon only buttons */}
                    <div className="xl:hidden flex items-center gap-2">
                        <Button
                            variant={viewMode === 'card' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('card')}
                            className="p-2"
                        >
                            <Grid3X3 className="h-4 w-4" />
                        </Button>
                        <Button
                            variant={viewMode === 'simple' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('simple')}
                            className="p-2"
                        >
                            <List className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </header>
            <div className="flex flex-1 flex-col gap-8 p-6 pb-20 lg:pb-6">
                {!activeBucket || activeCategories.length === 0 ? (
                    <div className="flex items-center justify-center h-96">
                        <div className="text-center text-muted-foreground">
                            <div className="text-6xl mb-4">🛍️</div>
                            <h2 className="text-xl font-semibold mb-2">
                                {!activeBucket ? "No bucket selected" : "No categories yet"}
                            </h2>
                            <p>
                                {!activeBucket
                                    ? "Select a bucket from the sidebar to get started"
                                    : "Start organizing your shopping list by adding categories"
                                }
                            </p>
                        </div>
                    </div>
                ) : viewMode === 'card' ? (
                    activeCategories.map((category) => (
                        <CategorySection
                            key={category.id}
                            category={category}
                            users={users}
                            onAddItem={handleAddItem}
                            onEditItem={handleEditItem}
                            onDeleteItem={handleDeleteItem}
                            onStatusChange={handleStatusChange}
                            onAssignUser={handleAssignUser}
                        />
                    ))
                ) : (
                    // Simple view implementation continues...
                    activeCategories.map((category) => (
                        <div key={category.id} className="space-y-3">
                            <div className="flex items-center justify-between">
                                <h2 className="text-lg font-semibold">{category.name}</h2>
                                <Badge variant="secondary">
                                    {category.items.length} {category.items.length === 1 ? 'item' : 'items'}
                                </Badge>
                            </div>
                            
                            <div className="space-y-2">
                                {category.items.map((item) => (
                                    <div key={item.id} className="flex items-center gap-3 p-2 rounded-md hover:bg-muted/50">
                                        <button
                                            onClick={() => handleSimpleToggleItem(item)}
                                            className={`flex-shrink-0 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                                                item.status === 'bought'
                                                    ? 'bg-green-500 border-green-500 text-white'
                                                    : 'border-muted-foreground/30 hover:border-muted-foreground/50'
                                            }`}
                                        >
                                            {item.status === 'bought' && <Check className="w-3 h-3" />}
                                        </button>
                                        <span className={`flex-1 ${item.status === 'bought' ? 'line-through text-muted-foreground' : ''}`}>
                                            {item.name}
                                        </span>
                                        {item.purchaseOptions && item.purchaseOptions.length > 0 && (
                                            <span className="text-sm text-muted-foreground">
                                                ${Math.min(...item.purchaseOptions.map(po => po.price))}
                                            </span>
                                        )}
                                    </div>
                                ))}
                                
                                <div className="flex items-center gap-2 pt-2">
                                    <Input
                                        placeholder="Add new item..."
                                        value={newItemInputs[category.id] || ''}
                                        onChange={(e) => setNewItemInputs(prev => ({ ...prev, [category.id]: e.target.value }))}
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter') {
                                                handleSimpleAddItem(category.id, newItemInputs[category.id] || '')
                                            }
                                        }}
                                        className="flex-1"
                                    />
                                    <Button
                                        size="sm"
                                        onClick={() => handleSimpleAddItem(category.id, newItemInputs[category.id] || '')}
                                        disabled={!newItemInputs[category.id]?.trim()}
                                    >
                                        <Plus className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))
                )}
            </div>

            <ItemModal
                isOpen={itemModalOpen}
                onClose={() => {
                    setItemModalOpen(false)
                    setEditingItem(null)
                }}
                categoryId={itemModalCategoryId}
                editingItem={editingItem}
                users={users}
                onSave={(itemData) => {
                    if (editingItem) {
                        updateItem(editingItem.id, itemData)
                    } else {
                        addItem({
                            ...itemData,
                            categoryId: itemModalCategoryId,
                            status: 'pending',
                            createdAt: new Date().toISOString(),
                            updatedAt: new Date().toISOString()
                        })
                    }
                    setItemModalOpen(false)
                    setEditingItem(null)
                }}
            />
        </>
    )
}
