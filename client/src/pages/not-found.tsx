import { <PERSON> } from "react-router"
import { But<PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"

export function NotFoundPage() {
  return (
    <>
      <header className="bg-background sticky top-0 flex h-14 shrink-0 items-center gap-2">
        <div className="flex flex-1 items-center gap-2 px-3">
          <SidebarTrigger />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <div className="flex items-center gap-2">
            <span className="text-xl">❓</span>
            <h1 className="font-semibold">Page Not Found</h1>
          </div>
        </div>
      </header>
      <div className="flex flex-1 flex-col gap-8 p-6">
        <div className="flex items-center justify-center h-96">
          <div className="text-center text-muted-foreground">
            <div className="text-6xl mb-4">404</div>
            <h2 className="text-xl font-semibold mb-2">Page Not Found</h2>
            <p className="mb-4">The page you're looking for doesn't exist.</p>
            <Button asChild>
              <Link to="/shopping-list">Go to Shopping List</Link>
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}
