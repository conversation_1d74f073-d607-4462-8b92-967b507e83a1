import React, { useState, useEffect } from "react"
import { Buck<PERSON> } from "shared"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface BucketModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (bucket: Omit<Bucket, 'id'>) => void
  onUpdate: (id: string, updates: Partial<Bucket>) => void
  editingBucket?: Bucket | null
}

const emojiOptions = [
  "🏠", "✈️", "🛍️", "🍕", "📚", "🎮", "🏃‍♂️", "🎨", 
  "💼", "🎵", "🌟", "🎯", "💡", "🔧", "🌱", "🎂"
]

export function BucketModal({
  isOpen,
  onClose,
  onSave,
  onUpdate,
  editingBucket,
}: BucketModalProps) {
  const [name, setName] = useState("")
  const [emoji, setEmoji] = useState("🛍️")
  const [budget, setBudget] = useState("")
  const [errors, setErrors] = useState<{ [key: string]: string }>({})

  useEffect(() => {
    if (editingBucket) {
      setName(editingBucket.name)
      setEmoji(editingBucket.emoji)
      setBudget(editingBucket.budget?.toString() || "")
    } else {
      setName("")
      setEmoji("🛍️")
      setBudget("")
    }
    setErrors({})
  }, [editingBucket, isOpen])

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}

    if (!name.trim()) {
      newErrors.name = "Bucket name is required"
    }

    if (budget && (isNaN(Number(budget)) || Number(budget) < 0)) {
      newErrors.budget = "Budget must be a positive number"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    const bucketData = {
      name: name.trim(),
      emoji,
      categories: editingBucket?.categories || [],
      budget: budget ? Number(budget) : undefined,
      ownerId: "user-1", // TODO: Get from auth context
    }

    if (editingBucket) {
      onUpdate(editingBucket.id, bucketData)
    } else {
      onSave(bucketData)
    }
    
    onClose()
  }

  const handleClose = () => {
    setErrors({})
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {editingBucket ? "Edit Bucket" : "Create New Bucket"}
          </DialogTitle>
          <DialogDescription>
            {editingBucket 
              ? "Update your bucket details below." 
              : "Create a new bucket to organize your shopping items."
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="bucket-name">Bucket Name</Label>
            <Input
              id="bucket-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g., Move to New Home"
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label>Choose Emoji</Label>
            <div className="grid grid-cols-8 gap-2">
              {emojiOptions.map((emojiOption) => (
                <button
                  key={emojiOption}
                  type="button"
                  onClick={() => setEmoji(emojiOption)}
                  className={`p-2 text-lg rounded border transition-colors ${
                    emoji === emojiOption 
                      ? "bg-blue-100 border-blue-500" 
                      : "bg-gray-50 border-gray-200 hover:bg-gray-100"
                  }`}
                >
                  {emojiOption}
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="bucket-budget">Budget (Optional)</Label>
            <Input
              id="bucket-budget"
              type="number"
              min="0"
              step="0.01"
              value={budget}
              onChange={(e) => setBudget(e.target.value)}
              placeholder="e.g., 1500.00"
              className={errors.budget ? "border-red-500" : ""}
            />
            {errors.budget && (
              <p className="text-sm text-red-500">{errors.budget}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit">
              {editingBucket ? "Update Bucket" : "Create Bucket"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}