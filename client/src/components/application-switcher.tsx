import * as React from "react"
import { ChevronDown, Plus } from "lucide-react"
import { useNavigate, useLocation } from "react-router"
import { Application } from "shared"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

export function ApplicationSwitcher({
  applications,
}: {
  applications: Application[]
}) {
  const navigate = useNavigate()
  const location = useLocation()

  // Determine active app based on current route
  const getActiveApp = () => {
    const path = location.pathname
    if (path.startsWith('/budget-planning')) {
      return applications.find(app => app.id === 'budget-planning')
    } else if (path.startsWith('/debt-management')) {
      return applications.find(app => app.id === 'debt-management')
    } else {
      return applications.find(app => app.id === 'shopping-list')
    }
  }

  const activeApp = getActiveApp() || applications[0]

  if (!activeApp) {
    return null
  }

  const handleAppSwitch = (appId: string) => {
    switch (appId) {
      case 'shopping-list':
        navigate('/shopping-list')
        break
      case 'budget-planning':
        navigate('/budget-planning')
        break
      case 'debt-management':
        navigate('/debt-management')
        break
      default:
        navigate('/shopping-list')
    }
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton className="w-fit px-1.5">
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-5 items-center justify-center rounded-md">
                <span className="text-xs">{activeApp.icon}</span>
              </div>
              <span className="truncate font-medium">{activeApp.name}</span>
              <ChevronDown className="opacity-50" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-64 rounded-lg"
            align="start"
            side="bottom"
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              Applications
            </DropdownMenuLabel>
            {applications.map((app, index) => (
              <DropdownMenuItem
                key={app.id}
                className="gap-2 p-2"
                onClick={() => handleAppSwitch(app.id)}
              >
                <div className="flex size-6 items-center justify-center rounded-xs border">
                  <span className="text-sm">{app.icon}</span>
                </div>
                {app.name}
                <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2 p-2">
              <div className="bg-background flex size-6 items-center justify-center rounded-md border">
                <Plus className="size-4" />
              </div>
              <div className="text-muted-foreground font-medium">Add application</div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}