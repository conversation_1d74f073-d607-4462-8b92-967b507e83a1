import * as React from "react"
import { MoreHorizontal, <PERSON>cil, Trash2, ExternalLink, ShoppingCart, Package, Clock, UserPlus } from "lucide-react"
import { Item, User } from "shared"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Badge,
} from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

const statusColors = {
  pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
  "in-cart": "bg-blue-100 text-blue-800 border-blue-200",
  bought: "bg-green-100 text-green-800 border-green-200",
}

const statusLabels = {
  pending: "Pending",
  "in-cart": "In Cart",
  bought: "Bought",
}

export function ItemCard({
  item,
  users = [],
  onEdit,
  onDelete,
  onStatusChange,
  onAssignUser,
}: {
  item: Item
  users?: User[]
  onEdit?: (item: Item) => void
  onDelete?: (itemId: string) => void
  onStatusChange?: (itemId: string, status: Item['status']) => void
  onAssignUser?: (itemId: string, userId: string | undefined) => void
}) {
  const assignedUser = users.find(user => user.id === item.assignedUserId)
  const selectedOption = item.purchaseOptions.find(opt => opt.id === item.selectedOptionId)
  const cheapestOption = item.purchaseOptions.reduce((prev, current) => 
    prev.price < current.price ? prev : current, item.purchaseOptions[0]
  )
  const displayOption = selectedOption || cheapestOption

  const priceRange = item.purchaseOptions.length > 1 ? {
    min: Math.min(...item.purchaseOptions.map(opt => opt.price)),
    max: Math.max(...item.purchaseOptions.map(opt => opt.price)),
  } : null

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger edit if clicking on interactive elements
    if (e.target instanceof Element) {
      const interactiveElements = ['BUTTON', 'A', 'INPUT', 'SELECT', 'TEXTAREA']
      const isInteractive = interactiveElements.includes(e.target.tagName) ||
        e.target.closest('button') ||
        e.target.closest('a') ||
        e.target.closest('[role="button"]') ||
        e.target.closest('[data-radix-dropdown-menu-trigger]') ||
        e.target.closest('[data-radix-dropdown-menu-content]')
      
      if (!isInteractive) {
        onEdit?.(item)
      }
    }
  }

  return (
    <Card 
      className="hover:shadow-md transition-shadow cursor-pointer" 
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-base">{item.name}</CardTitle>
            {item.notes && (
              <CardDescription className="mt-1 text-sm">
                {item.notes}
              </CardDescription>
            )}
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit?.(item)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              {onStatusChange && (
                <>
                  <DropdownMenuSeparator />
                  {item.status !== 'pending' && (
                    <DropdownMenuItem onClick={() => onStatusChange(item.id, 'pending')}>
                      <Clock className="mr-2 h-4 w-4" />
                      Mark as Pending
                    </DropdownMenuItem>
                  )}
                  {item.status !== 'in-cart' && (
                    <DropdownMenuItem onClick={() => onStatusChange(item.id, 'in-cart')}>
                      <ShoppingCart className="mr-2 h-4 w-4" />
                      Add to Cart
                    </DropdownMenuItem>
                  )}
                  {item.status !== 'bought' && (
                    <DropdownMenuItem onClick={() => onStatusChange(item.id, 'bought')}>
                      <Package className="mr-2 h-4 w-4" />
                      Mark as Bought
                    </DropdownMenuItem>
                  )}
                </>
              )}
              {onAssignUser && users.length > 0 && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onAssignUser(item.id, undefined)}
                    disabled={!item.assignedUserId}
                  >
                    <UserPlus className="mr-2 h-4 w-4" />
                    Unassign
                  </DropdownMenuItem>
                  {users.map((user) => (
                    <DropdownMenuItem
                      key={user.id}
                      onClick={() => onAssignUser(item.id, user.id)}
                      disabled={item.assignedUserId === user.id}
                    >
                      <div 
                        className="mr-2 h-4 w-4 rounded-full border-2" 
                        style={{ backgroundColor: `${user.color}20`, borderColor: user.color }}
                      />
                      Assign to {user.name}
                    </DropdownMenuItem>
                  ))}
                </>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="text-destructive"
                onClick={() => onDelete?.(item.id)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex items-center gap-2 mb-3">
          <Badge 
            variant="outline" 
            className={statusColors[item.status]}
          >
            {statusLabels[item.status]}
          </Badge>
          {assignedUser && (
            <Badge 
              variant="outline"
              style={{ 
                backgroundColor: `${assignedUser.color}20`,
                borderColor: assignedUser.color,
                color: assignedUser.color 
              }}
            >
              {assignedUser.name}
            </Badge>
          )}
        </div>
        
        {item.purchaseOptions.length > 0 && (
          <div className="space-y-2">
            {item.purchaseOptions.length === 1 ? (
              // Single option display
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">{displayOption?.storeName}</span>
                  <div className="font-bold">
                    ${displayOption?.price.toFixed(2)}
                  </div>
                </div>
                
                {displayOption?.link && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => window.open(displayOption.link, '_blank')}
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    View Product
                  </Button>
                )}
              </div>
            ) : (
              // Multiple options comparison
              <div className="space-y-2">
                <div className="text-xs font-medium text-muted-foreground mb-2">
                  Price Comparison ({item.purchaseOptions.length} options)
                </div>
                {item.purchaseOptions
                  .sort((a, b) => a.price - b.price)
                  .map((option, index) => {
                    const isSelected = option.id === item.selectedOptionId
                    const isCheapest = option.price === Math.min(...item.purchaseOptions.map(opt => opt.price))
                    
                    return (
                      <div 
                        key={option.id} 
                        className={`flex items-center justify-between text-xs p-2 rounded border ${
                          isSelected ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'
                        }`}
                      >
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{option.storeName}</span>
                          {isCheapest && (
                            <Badge variant="outline" className="bg-green-100 text-green-700 border-green-200 text-xs">
                              Best Price
                            </Badge>
                          )}
                          {isSelected && (
                            <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-200 text-xs">
                              Selected
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-bold">${option.price.toFixed(2)}</span>
                          {option.link && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation()
                                window.open(option.link, '_blank')
                              }}
                            >
                              <ExternalLink className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    )
                  })}
                
                {priceRange && (
                  <div className="text-xs text-muted-foreground text-center pt-1 border-t">
                    You could save ${(priceRange.max - priceRange.min).toFixed(2)} by choosing the cheapest option
                  </div>
                )}
              </div>
            )}
          </div>
        )}
        
        {item.purchaseOptions.length === 0 && (
          <div className="text-sm text-muted-foreground text-center py-2">
            No purchase options yet
          </div>
        )}
      </CardContent>
    </Card>
  )
}