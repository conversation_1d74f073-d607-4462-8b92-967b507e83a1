import * as React from "react"
import { Category } from "shared"
import { useShopping } from "@/contexts/shopping-context"
import { Progress } from "@/components/ui/progress"
import {
  Drawer,
  Drawer<PERSON>ontent,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer"
import { CostSummaryFirst, CostSummaryLast } from "@/components/cost-summary-parts"
import { ProgressTracker } from "@/components/progress-tracker"

interface MobileTotalsBarProps {
  categories: Category[]
  currentCategoryId?: string
  budget?: number
}

export function MobileTotalsBar({
  categories,
  currentCategoryId,
  budget
}: MobileTotalsBarProps) {
  const { activeBucket, updateBucket } = useShopping()
  const calculateBucketTotal = () => {
    return categories.reduce((total, category) => {
      return total + (category.items || []).reduce((categoryTotal, item) => {
        if (!item.purchaseOptions || item.purchaseOptions.length === 0) return categoryTotal

        const selectedOption = item.purchaseOptions.find(opt => opt.id === item.selectedOptionId)
        const cheapestOption = item.purchaseOptions.reduce((prev, current) =>
          (prev.price || 0) < (current.price || 0) ? prev : current, item.purchaseOptions[0]
        )

        return categoryTotal + (selectedOption?.price || cheapestOption.price || 0)
      }, 0)
    }, 0)
  }

  const bucketTotal = calculateBucketTotal()
  const budgetUsed = budget ? (bucketTotal / budget) * 100 : 0
  const budgetStatus = budget ? (
    budgetUsed <= 50 ? 'safe' :
      budgetUsed <= 80 ? 'warning' :
        'danger'
  ) : null

  return (
    <div className="lg:hidden">
      <Drawer>
        <DrawerTrigger asChild>
          <div className="absolute bottom-0 left-0 right-0 bg-background border-t border-border z-40">
            <div className="flex items-center justify-between px-4 py-3">
              <div className="flex flex-col">
                <div className="text-sm font-medium">
                  {budget ? `$${bucketTotal.toFixed(2)} / $${budget.toFixed(2)}` : `Total: $${bucketTotal.toFixed(2)}`}
                </div>
                {budget && (
                  <div className="text-xs text-muted-foreground">
                    {bucketTotal > budget ? `Over by $${(bucketTotal - budget).toFixed(2)}` : `$${(budget - bucketTotal).toFixed(2)} remaining`}
                  </div>
                )}
              </div>
              <div className="text-xs text-muted-foreground">
                Tap for details
              </div>
            </div>
            {budget && (
              <div className="px-4 pb-1">
                <div className="h-0.5 bg-muted rounded-full overflow-hidden">
                  <div
                    className={`h-full transition-all duration-300 ${budgetStatus === 'danger' ? 'bg-red-500' :
                      budgetStatus === 'warning' ? 'bg-yellow-500' :
                        'bg-green-500'
                      }`}
                    style={{ width: `${Math.min(budgetUsed, 100)}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        </DrawerTrigger>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>{budget ? 'Budget & Cost Summary' : 'Cost Summary'}</DrawerTitle>
          </DrawerHeader>
          <div className="px-4 pb-4 space-y-4 max-h-[70vh] overflow-y-auto">
            <CostSummaryFirst
              categories={categories}
              currentCategoryId={currentCategoryId}
              budget={budget}
              activeBucket={activeBucket}
              onUpdateBucket={updateBucket}
            />

            <ProgressTracker categories={categories} />

            <CostSummaryLast categories={categories} />
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  )
}