import React, { useState, useEffect, useRef } from "react"
import { Plus, Trash, Pencil, ExternalLink } from "lucide-react"
import { Item, User, PurchaseOption, ItemStatus } from "shared"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface ItemModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (item: Omit<Item, 'id'>) => void
  onUpdate?: (id: string, updates: Partial<Item>) => void
  categoryId: string
  users: User[]
  editingItem?: Item | null
}

export function ItemModal({
  isOpen,
  onClose,
  onSave,
  onUpdate,
  categoryId,
  users,
  editingItem,
}: ItemModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    notes: "",
    status: "pending" as ItemStatus,
    assignedUserId: "unassigned",
    selectedOptionId: "auto",
  })
  const [purchaseOptions, setPurchaseOptions] = useState<Omit<PurchaseOption, 'id' | 'itemId'>[]>([])
  const [editingOptionIndex, setEditingOptionIndex] = useState<number | null>(null)
  const storeNameInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (editingItem) {
      setFormData({
        name: editingItem.name,
        notes: editingItem.notes,
        status: editingItem.status,
        assignedUserId: editingItem.assignedUserId || "unassigned",
        selectedOptionId: editingItem.selectedOptionId || "auto",
      })
      setPurchaseOptions(editingItem.purchaseOptions?.map(({ id, itemId, ...option }) => option) || [])
    } else {
      setFormData({
        name: "",
        notes: "",
        status: "pending",
        assignedUserId: "unassigned",
        selectedOptionId: "auto",
      })
      setPurchaseOptions([])
    }
    setEditingOptionIndex(null)
  }, [editingItem, isOpen])

  // Auto focus on store name input when editing option index changes
  useEffect(() => {
    if (editingOptionIndex !== null && storeNameInputRef.current) {
      setTimeout(() => {
        storeNameInputRef.current?.focus()
      }, 100)
    }
  }, [editingOptionIndex])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim()) return

    const purchaseOptionsWithIds: PurchaseOption[] = purchaseOptions.map((option, index) => ({
      ...option,
      id: editingItem?.purchaseOptions?.[index]?.id || `${Date.now()}-${index}`,
      itemId: editingItem?.id || '',
    }))

    const itemData = {
      name: formData.name.trim(),
      notes: formData.notes.trim(),
      categoryId,
      status: formData.status,
      assignedUserId: formData.assignedUserId === "unassigned" ? undefined : formData.assignedUserId,
      selectedOptionId: formData.selectedOptionId === "auto" ? undefined : formData.selectedOptionId,
      purchaseOptions: purchaseOptionsWithIds,
    }

    if (editingItem && onUpdate) {
      onUpdate(editingItem.id, itemData)
    } else {
      onSave(itemData)
    }

    onClose()
  }

  const addPurchaseOption = () => {
    const newIndex = purchaseOptions.length
    setPurchaseOptions([...purchaseOptions, { storeName: "", price: 0, link: "" }])
    setEditingOptionIndex(newIndex)
  }

  const updatePurchaseOption = (index: number, field: keyof Omit<PurchaseOption, 'id' | 'itemId'>, value: string | number) => {
    const updated = [...purchaseOptions]
    updated[index] = { ...updated[index], [field]: value }
    setPurchaseOptions(updated)
  }

  const removePurchaseOption = (index: number) => {
    const updated = purchaseOptions.filter((_, i) => i !== index)
    setPurchaseOptions(updated)

    // Reset selected option if it was removed
    if (editingItem?.purchaseOptions?.[index]?.id === formData.selectedOptionId) {
      setFormData(prev => ({ ...prev, selectedOptionId: "auto" }))
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingItem ? "Edit Item" : "Add New Item"}
          </DialogTitle>
          <DialogDescription>
            {editingItem
              ? "Update the item details below."
              : "Add a new item to your shopping list."
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium mb-2">
                Item Name *
              </label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter item name"
                required
              />
            </div>

            <div>
              <label htmlFor="notes" className="block text-sm font-medium mb-2">
                Notes
              </label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Add any notes or description"
                className="min-h-20"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Status</label>
                <Select
                  value={formData.status}
                  onValueChange={(value: ItemStatus) =>
                    setFormData(prev => ({ ...prev, status: value }))
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="in-cart">In Cart</SelectItem>
                    <SelectItem value="bought">Bought</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Assign to User</label>
                <Select
                  value={formData.assignedUserId}
                  onValueChange={(value) =>
                    setFormData(prev => ({ ...prev, assignedUserId: value }))
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select user" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unassigned">Unassigned</SelectItem>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium">Purchase Options</label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addPurchaseOption}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Option
                </Button>
              </div>

              <div className="space-y-3">
                {purchaseOptions.map((option, index) => (
                  <Card key={index} className="py-3">
                    {editingItem && editingOptionIndex !== index ? (
                      // Display mode for existing options in edit mode
                      <CardContent className="px-4">
                        <div className="flex items-center justify-between">
                          <div
                            className="flex-1 cursor-pointer hover:bg-gray-50 p-2 rounded"
                            onClick={() => option.link && window.open(option.link, '_blank')}
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-medium text-sm">{option.storeName}</div>
                                <div className="text-lg font-semibold">${option.price?.toFixed(2) || '0.00'}</div>
                                {option.link && (
                                  <div className="flex items-center text-blue-600 text-xs mt-1">
                                    <ExternalLink className="h-3 w-3 mr-1" />
                                    View Product
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => setEditingOptionIndex(index)}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removePurchaseOption(index)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    ) : (
                      // Edit mode for new options or when editing an existing option
                      <>
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-sm">Option {index + 1}</CardTitle>
                            <div className="flex gap-2">
                              {editingItem && editingOptionIndex === index && (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setEditingOptionIndex(null)}
                                >
                                  Done
                                </Button>
                              )}
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removePurchaseOption(index)}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <label className="block text-xs font-medium mb-1">Name</label>
                              <Input
                                ref={editingOptionIndex === index ? storeNameInputRef : undefined}
                                value={option.storeName}
                                onChange={(e) => updatePurchaseOption(index, 'storeName', e.target.value)}
                                placeholder="e.g., Amazon, IKEA"
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium mb-1">Price ($)</label>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                value={option.price || ""}
                                onChange={(e) => updatePurchaseOption(index, 'price', parseFloat(e.target.value) || 0)}
                                placeholder="0.00"
                              />
                            </div>
                          </div>
                          <div>
                            <label className="block text-xs font-medium mb-1">Product Link</label>
                            <Input
                              value={option.link}
                              onChange={(e) => updatePurchaseOption(index, 'link', e.target.value)}
                              placeholder="https://..."
                            />
                          </div>
                        </CardContent>
                      </>
                    )}
                  </Card>
                ))}
              </div>

              {purchaseOptions.length > 1 && (
                <div>
                  <label className="block text-sm font-medium mt-4 mb-2">Preferred Option</label>
                  <Select
                    value={formData.selectedOptionId}
                    onValueChange={(value) =>
                      setFormData(prev => ({ ...prev, selectedOptionId: value }))
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select preferred option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">Auto (Cheapest)</SelectItem>
                      {purchaseOptions.map((option, index) => (
                        <SelectItem
                          key={index}
                          value={editingItem?.purchaseOptions?.[index]?.id || `${Date.now()}-${index}`}
                        >
                          {option.storeName} - ${option.price?.toFixed(2) || '0.00'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              {editingItem ? "Update Item" : "Add Item"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}