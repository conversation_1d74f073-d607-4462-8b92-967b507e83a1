"use client"

import * as React from "react"
import {
  Home,
  Inbox,
  Plus,
  Search,
  Sparkles,
} from "lucide-react"
import { Application, Bucket, Category, User } from "shared"

import { ApplicationSwitcher } from "@/components/application-switcher"
import { BucketList } from "@/components/bucket-list"
import { CategoryNavigation } from "@/components/category-navigation"
import { NavMain } from "@/components/nav-main"
import { UserProfile } from "@/components/user-profile"
import { BucketModal } from "@/components/bucket-modal"
import { CategoryModal } from "@/components/category-modal"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import { useShopping } from "@/contexts/shopping-context"

// Sample data for shopping list application
const data = {
  applications: [
    {
      id: "shopping-list",
      name: "Shopping List",
      icon: "🛍️",
      isActive: true,
    },
    {
      id: "budget-planning",
      name: "Budget Planning",
      icon: "💰",
      isActive: false,
    },
    {
      id: "debt-management",
      name: "Debt Management",
      icon: "📊",
      isActive: false,
    },
  ] as Application[],
  navMain: [
    {
      title: "Home",
      url: "#",
      icon: Home,
      isActive: true,
    },
    {
      title: "Search",
      url: "#",
      icon: Search,
    },
    {
      title: "Ask AI",
      url: "#",
      icon: Sparkles,
    },
    {
      title: "Inbox",
      url: "#",
      icon: Inbox,
      // badge: "3",
    },
  ],
  buckets: [
    {
      id: "move-to-new-home",
      name: "Move to New Home",
      emoji: "🏠",
      categories: [
        {
          id: "furniture",
          name: "Furniture",
          bucketId: "move-to-new-home",
          items: [
            {
              id: "sofa",
              name: "Living Room Sofa",
              notes: "Need something comfortable for 4 people",
              categoryId: "furniture",
              purchaseOptions: [
                {
                  id: "ikea-sofa",
                  storeName: "IKEA",
                  price: 799,
                  link: "https://ikea.com/sofa",
                },
              ],
              status: "pending" as const,
            },
          ],
        },
        {
          id: "workspace",
          name: "Workspace",
          bucketId: "move-to-new-home",
          items: [],
        },
      ],
    },
    {
      id: "travel-to-dubai",
      name: "Travel to Dubai",
      emoji: "✈️",
      categories: [
        {
          id: "accommodations",
          name: "Accommodations",
          bucketId: "travel-to-dubai",
          items: [],
        },
        {
          id: "clothes",
          name: "Clothes",
          bucketId: "travel-to-dubai",
          items: [],
        },
      ],
    },
  ] as Bucket[],
  user: {
    id: "user-1",
    name: "Tegar Imansyah",
    color: "#3b82f6",
  } as User,
}

export function SidebarLeft({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const {
    applications,
    buckets,
    users,
    activeBucketId,
    activeCategories,
    activeCategoryId,
    setActiveBucket,
    setActiveCategory,
    addBucket,
    updateBucket,
    deleteBucket,
    addCategory,
    updateCategory,
    deleteCategory
  } = useShopping()

  // Modal states
  const [bucketModalOpen, setBucketModalOpen] = React.useState(false)
  const [categoryModalOpen, setCategoryModalOpen] = React.useState(false)
  const [editingBucket, setEditingBucket] = React.useState<Bucket | null>(null)
  const [editingCategory, setEditingCategory] = React.useState<Category | null>(null)

  // Bucket CRUD handlers
  const handleCreateBucket = () => {
    setEditingBucket(null)
    setBucketModalOpen(true)
  }

  const handleEditBucket = (bucket: Bucket) => {
    setEditingBucket(bucket)
    setBucketModalOpen(true)
  }

  const handleDeleteBucket = (bucketId: string) => {
    deleteBucket(bucketId)
  }

  const handleCloseBucketModal = () => {
    setBucketModalOpen(false)
    setEditingBucket(null)
  }

  // Category CRUD handlers
  const handleAddCategory = () => {
    if (!activeBucketId) return
    setEditingCategory(null)
    setCategoryModalOpen(true)
  }

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category)
    setCategoryModalOpen(true)
  }

  const handleDeleteCategory = (categoryId: string) => {
    deleteCategory(categoryId)
  }

  const handleCloseCategoryModal = () => {
    setCategoryModalOpen(false)
    setEditingCategory(null)
  }

  const navMainItems = [
    {
      title: "Search",
      url: "#",
      icon: Search,
    },
    {
      title: "Ask AI",
      url: "#",
      icon: Sparkles,
    },
    {
      title: "Home",
      url: "#",
      icon: Home,
      isActive: true,
    },
    {
      title: "Inbox",
      url: "#",
      icon: Inbox,
      badge: "3",
    },
  ]

  return (
    <>
      <Sidebar className="border-r-0" {...props}>
        <SidebarHeader>
          <ApplicationSwitcher applications={applications} />
          <NavMain items={navMainItems} />
        </SidebarHeader>
        <SidebarContent>
          <BucketList
            buckets={buckets}
            activeBucketId={activeBucketId || undefined}
            onBucketSelect={setActiveBucket}
            onEditBucket={handleEditBucket}
            onDeleteBucket={handleDeleteBucket}
            onCreateBucket={handleCreateBucket}
          />
          <CategoryNavigation
            categories={activeCategories}
            activeCategoryId={activeCategoryId || undefined}
            onCategorySelect={setActiveCategory}
            onAddCategory={handleAddCategory}
            onEditCategory={handleEditCategory}
            onDeleteCategory={handleDeleteCategory}
          />
        </SidebarContent>
        <SidebarFooter>
          <UserProfile user={users[0]} />
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>

      <BucketModal
        isOpen={bucketModalOpen}
        onClose={handleCloseBucketModal}
        onSave={addBucket}
        onUpdate={updateBucket}
        editingBucket={editingBucket}
      />

      <CategoryModal
        isOpen={categoryModalOpen}
        onClose={handleCloseCategoryModal}
        onSave={addCategory}
        onUpdate={updateCategory}
        bucketId={activeBucketId || ""}
        editingCategory={editingCategory}
      />
    </>
  )
}
