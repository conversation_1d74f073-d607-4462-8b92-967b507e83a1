import { ChevronR<PERSON>, MoreHorizontal, Plus, <PERSON>ci<PERSON>, Trash2 } from "lucide-react"
import { Category } from "shared"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar"

export function CategoryNavigation({
  categories,
  activeCategoryId,
  onCategorySelect,
  onAddCategory,
  onEditCategory,
  onDeleteCategory,
}: {
  categories: Category[]
  activeCategoryId?: string
  onCategorySelect?: (categoryId: string) => void
  onAddCategory?: () => void
  onEditCategory?: (category: Category) => void
  onDeleteCategory?: (categoryId: string) => void
}) {
  const { isMobile } = useSidebar()

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Categories</SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton 
              onClick={onAddCategory}
              className="text-sidebar-foreground/70"
            >
              <Plus />
              <span>Add Category</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          {categories.map((category) => (
            <SidebarMenuItem key={category.id}>
              <SidebarMenuButton 
                asChild
                isActive={category.id === activeCategoryId}
              >
                <button 
                  onClick={() => onCategorySelect?.(category.id)}
                  title={category.name}
                  className="w-full text-left"
                >
                  <span>📁</span>
                  <span>{category.name}</span>
                  <span className="ml-auto text-xs text-muted-foreground">
                    {category.items.length}
                  </span>
                </button>
              </SidebarMenuButton>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuAction showOnHover>
                    <MoreHorizontal />
                    <span className="sr-only">More</span>
                  </SidebarMenuAction>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-56 rounded-lg"
                  side={isMobile ? "bottom" : "right"}
                  align={isMobile ? "end" : "start"}
                >
                  <DropdownMenuItem onClick={() => onEditCategory?.(category)}>
                    <Pencil className="text-muted-foreground" />
                    <span>Edit Category</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    className="text-destructive"
                    onClick={() => {
                      if (confirm(`Are you sure you want to delete "${category.name}"? This will also delete all items in this category.`)) {
                        onDeleteCategory?.(category.id)
                      }
                    }}
                  >
                    <Trash2 className="text-muted-foreground" />
                    <span>Delete Category</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}