import {
  ArrowUp<PERSON><PERSON>,
  Link,
  MoreHorizontal,
  Pencil,
  Plus,
  Trash2,
} from "lucide-react"
import { Bucket } from "shared"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"

export function BucketList({
  buckets,
  activeBucketId,
  onBucketSelect,
  onEditBucket,
  onDeleteBucket,
  onCreateBucket,
}: {
  buckets: Bucket[]
  activeBucketId?: string
  onBucketSelect?: (bucketId: string) => void
  onEditBucket?: (bucket: Bucket) => void
  onDeleteBucket?: (bucketId: string) => void
  onCreateBucket?: () => void
}) {
  const { isMobile } = useSidebar()

  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarGroupLabel>Buckets</SidebarGroupLabel>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton 
            onClick={onCreateBucket}
            className="text-sidebar-foreground/70"
          >
            <Plus />
            <span>Create a Bucket</span>
          </SidebarMenuButton>
        </SidebarMenuItem>
        {buckets.map((bucket) => (
          <SidebarMenuItem key={bucket.id}>
            <SidebarMenuButton 
              asChild
              isActive={bucket.id === activeBucketId}
            >
              <button 
                onClick={() => onBucketSelect?.(bucket.id)}
                title={bucket.name}
                className="w-full text-left"
              >
                <span>{bucket.emoji}</span>
                <span>{bucket.name}</span>
              </button>
            </SidebarMenuButton>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuAction showOnHover>
                  <MoreHorizontal />
                  <span className="sr-only">More</span>
                </SidebarMenuAction>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-56 rounded-lg"
                side={isMobile ? "bottom" : "right"}
                align={isMobile ? "end" : "start"}
              >
                <DropdownMenuItem onClick={() => onEditBucket?.(bucket)}>
                  <Pencil className="text-muted-foreground" />
                  <span>Edit Bucket</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  className="text-destructive"
                  onClick={() => {
                    if (confirm(`Are you sure you want to delete "${bucket.name}"? This will also delete all categories and items in this bucket.`)) {
                      onDeleteBucket?.(bucket.id)
                    }
                  }}
                >
                  <Trash2 className="text-muted-foreground" />
                  <span>Delete Bucket</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  )
}