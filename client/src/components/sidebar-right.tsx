import * as React from "react"
import { Category } from "shared"

import { CostSummaryFirst, CostSummaryLast } from "@/components/cost-summary-parts"
import { ProgressTracker } from "@/components/progress-tracker"
import {
  Sidebar,
  SidebarContent,
  SidebarRail,
  SidebarSeparator,
} from "@/components/ui/sidebar"


export function SidebarRight({
  categories = [],
  currentCategoryId,
  budget,
  ...props
}: React.ComponentProps<typeof Sidebar> & {
  categories?: Category[]
  currentCategoryId?: string
  budget?: number
}) {
  return (
    <Sidebar
      collapsible="none"
      className="sticky top-0 hidden h-svh border-l lg:flex"
      style={{ width: '320px', minWidth: '320px' }}
      {...props}
    >
      <SidebarContent className="gap-4 p-4">
        {/* Budget Tracking, Current Category, Price Analysis sections */}
        <CostSummaryFirst 
          categories={categories} 
          currentCategoryId={currentCategoryId}
          budget={budget}
        />
        
        {/* Progress Analytics - positioned after Price Analysis */}
        <ProgressTracker categories={categories} />
        
        {/* Bucket Overview and Quick Stats sections */}
        <CostSummaryLast 
          categories={categories} 
        />
      </SidebarContent>
    </Sidebar>
  )
}
