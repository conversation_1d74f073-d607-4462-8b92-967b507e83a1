import { Category } from "shared"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { TrendingUp, ShoppingCart, CheckCircle, Clock, DollarSign } from "lucide-react"

export function ProgressTracker({
  categories = [],
}: {
  categories?: Category[]
}) {
  const allItems = categories.flatMap(cat => cat.items)
  
  const pendingItems = allItems.filter(item => item.status === 'pending')
  const inCartItems = allItems.filter(item => item.status === 'in-cart')
  const boughtItems = allItems.filter(item => item.status === 'bought')
  
  const totalItems = allItems.length
  const completionPercentage = totalItems > 0 ? (boughtItems.length / totalItems) * 100 : 0
  const shoppingPercentage = totalItems > 0 ? ((inCartItems.length + boughtItems.length) / totalItems) * 100 : 0
  
  // Price analytics
  const itemsWithPrices = allItems.filter(item => item.purchaseOptions.length > 0)
  const boughtItemsValue = boughtItems.reduce((total, item) => {
    if (item.purchaseOptions.length === 0) return total
    const selectedOption = item.purchaseOptions.find(opt => opt.id === item.selectedOptionId)
    const cheapestOption = item.purchaseOptions.reduce((prev, current) => 
      prev.price < current.price ? prev : current, item.purchaseOptions[0]
    )
    return total + (selectedOption?.price || cheapestOption.price)
  }, 0)
  
  const totalBucketValue = allItems.reduce((total, item) => {
    if (item.purchaseOptions.length === 0) return total
    const selectedOption = item.purchaseOptions.find(opt => opt.id === item.selectedOptionId)
    const cheapestOption = item.purchaseOptions.reduce((prev, current) => 
      prev.price < current.price ? prev : current, item.purchaseOptions[0]
    )
    return total + (selectedOption?.price || cheapestOption.price)
  }, 0)
  
  // Most active category
  const categoryActivity = categories.map(cat => ({
    ...cat,
    activityScore: cat.items.filter(item => item.status !== 'pending').length
  })).sort((a, b) => b.activityScore - a.activityScore)
  
  const mostActiveCategory = categoryActivity[0]

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Progress Analytics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {totalItems > 0 ? (
            <>
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="flex items-center gap-1">
                      <CheckCircle className="h-3 w-3" />
                      Completed
                    </span>
                    <span className="font-medium">{Math.round(completionPercentage)}%</span>
                  </div>
                  <Progress value={completionPercentage} className="h-2" />
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="flex items-center gap-1">
                      <ShoppingCart className="h-3 w-3" />
                      Shopping
                    </span>
                    <span className="font-medium">{Math.round(shoppingPercentage)}%</span>
                  </div>
                  <Progress value={shoppingPercentage} className="h-2 [&>div]:bg-blue-500" />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-2">
                <div className="text-center">
                  <Badge 
                    variant="outline" 
                    className="bg-yellow-100 text-yellow-800 border-yellow-200 w-full justify-center"
                  >
                    {pendingItems.length}
                  </Badge>
                  <div className="text-xs text-muted-foreground mt-1 flex items-center justify-center gap-1">
                    <Clock className="h-3 w-3" />
                    Pending
                  </div>
                </div>
                
                <div className="text-center">
                  <Badge 
                    variant="outline" 
                    className="bg-blue-100 text-blue-800 border-blue-200 w-full justify-center"
                  >
                    {inCartItems.length}
                  </Badge>
                  <div className="text-xs text-muted-foreground mt-1 flex items-center justify-center gap-1">
                    <ShoppingCart className="h-3 w-3" />
                    In Cart
                  </div>
                </div>
                
                <div className="text-center">
                  <Badge 
                    variant="outline" 
                    className="bg-green-100 text-green-800 border-green-200 w-full justify-center"
                  >
                    {boughtItems.length}
                  </Badge>
                  <div className="text-xs text-muted-foreground mt-1 flex items-center justify-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Bought
                  </div>
                </div>
              </div>

              {totalBucketValue > 0 && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                      <DollarSign className="h-3 w-3" />
                      SPENDING PROGRESS
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Purchased</span>
                      <span className="font-medium">${boughtItemsValue.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Total Budget</span>
                      <span className="font-medium">${totalBucketValue.toFixed(2)}</span>
                    </div>
                    <Progress 
                      value={totalBucketValue > 0 ? (boughtItemsValue / totalBucketValue) * 100 : 0} 
                      className="h-2 [&>div]:bg-green-500" 
                    />
                  </div>
                </>
              )}

              {mostActiveCategory && mostActiveCategory.activityScore > 0 && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-muted-foreground">MOST ACTIVE CATEGORY</div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{mostActiveCategory.name}</span>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                        {mostActiveCategory.activityScore} active items
                      </Badge>
                    </div>
                  </div>
                </>
              )}

              <Separator />
              <div className="space-y-2">
                <div className="text-xs font-medium text-muted-foreground">BY CATEGORY</div>
                {categories.map((category) => {
                  const categoryItems = category.items
                  const categoryBought = categoryItems.filter(item => item.status === 'bought').length
                  const categoryTotal = categoryItems.length
                  const categoryPercentage = categoryTotal > 0 ? (categoryBought / categoryTotal) * 100 : 0
                  
                  if (categoryTotal === 0) return null
                  
                  return (
                    <div key={category.id} className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span className="text-muted-foreground">{category.name}</span>
                        <span className="text-muted-foreground">{categoryBought}/{categoryTotal}</span>
                      </div>
                      <Progress value={categoryPercentage} className="h-1" />
                    </div>
                  )
                })}
              </div>
            </>
          ) : (
            <div className="text-center text-muted-foreground text-sm py-4">
              <div className="text-2xl mb-2">📊</div>
              <div>No items to track yet</div>
              <div className="text-xs mt-1">Progress will appear when you add items</div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}