import { Plus } from "lucide-react"
import { Category, User, Item } from "shared"

import { Button } from "@/components/ui/button"
import { ItemCard } from "@/components/item-card"

export function CategorySection({
  category,
  users = [],
  onAddItem,
  onEditItem,
  onDeleteItem,
  onStatusChange,
  onAssignUser,
}: {
  category: Category
  users?: User[]
  onAddItem?: (categoryId: string) => void
  onEditItem?: (item: Item) => void
  onDeleteItem?: (itemId: string) => void
  onStatusChange?: (itemId: string, status: Item['status']) => void
  onAssignUser?: (itemId: string, userId: string | undefined) => void
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">{category.name}</h2>
          <p className="text-sm text-muted-foreground">
            {category.items.length} {category.items.length === 1 ? 'item' : 'items'}
          </p>
        </div>
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => onAddItem?.(category.id)}
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Item
        </Button>
      </div>
      
      {category.items.length === 0 ? (
        <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
          <div className="text-muted-foreground">
            <Plus className="mx-auto h-12 w-12 mb-4 opacity-25" />
            <h3 className="text-lg font-medium mb-2">No items yet</h3>
            <p className="text-sm mb-4">
              Start by adding items to this category
            </p>
            <Button 
              variant="outline"
              onClick={() => onAddItem?.(category.id)}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Your First Item
            </Button>
          </div>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {category.items.map((item) => (
            <ItemCard
              key={item.id}
              item={item}
              users={users}
              onEdit={onEditItem}
              onDelete={onDeleteItem}
              onStatusChange={onStatusChange}
              onAssignUser={onAssignUser}
            />
          ))}
        </div>
      )}
    </div>
  )
}