import { createBrowserRouter, createRoutesFromElements, Route } from "react-router";
import { RootLayout } from "./layouts/root-layout";
import { ShoppingListPage } from "./pages/shopping-list";
import { BudgetPlanningPage } from "./pages/budget-planning";
import { DebtManagementPage } from "./pages/debt-management";
import { BucketPage } from "./pages/bucket";
import { CategoryPage } from "./pages/category";
import { SettingsPage } from "./pages/settings";
import { NotFoundPage } from "./pages/not-found";

export const router = createBrowserRouter(
  createRoutesFromElements(
    <Route path="/" element={<RootLayout />}>
      {/* Default redirect to shopping list */}
      <Route index element={<ShoppingListPage />} />
      
      {/* Application routes */}
      <Route path="shopping-list" element={<ShoppingListPage />}>
        <Route path="bucket/:bucketId" element={<BucketPage />}>
          <Route path="category/:categoryId" element={<CategoryPage />} />
        </Route>
      </Route>
      
      <Route path="budget-planning" element={<BudgetPlanningPage />} />
      <Route path="debt-management" element={<DebtManagementPage />} />
      
      {/* Settings and user routes */}
      <Route path="settings" element={<SettingsPage />} />
      
      {/* 404 page */}
      <Route path="*" element={<NotFoundPage />} />
    </Route>
  )
);
