import * as React from "react"
import { SidebarLeft } from "@/components/sidebar-left"
import { SidebarRight } from "@/components/sidebar-right"
import { CategorySection } from "@/components/category-section"
import { ItemModal } from "@/components/item-modal"
import { MobileTotalsBar } from "@/components/mobile-totals-bar"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
    SidebarInset,
    SidebarProvider,
    SidebarTrigger,
} from "@/components/ui/sidebar"
import { useShopping } from "@/contexts/shopping-context"
import { Item, ItemStatus } from "shared"
import { Grid3X3, List, Plus, Check } from "lucide-react"

export default function Page() {
    const {
        activeBucket,
        activeCategories,
        activeCategoryId,
        users,
        addItem,
        updateItem,
        deleteItem,
        addCategory
    } = useShopping()

    const [itemModalOpen, setItemModalOpen] = React.useState(false)
    const [itemModalCategoryId, setItemModalCategoryId] = React.useState<string>("")
    const [editingItem, setEditingItem] = React.useState<Item | null>(null)
    const [viewMode, setViewMode] = React.useState<'card' | 'simple'>('simple')
    const [newItemInputs, setNewItemInputs] = React.useState<{ [categoryId: string]: string }>({})

    const handleAddItem = (categoryId: string) => {
        setItemModalCategoryId(categoryId)
        setEditingItem(null)
        setItemModalOpen(true)
    }

    const handleEditItem = (item: Item) => {
        setItemModalCategoryId(item.categoryId)
        setEditingItem(item)
        setItemModalOpen(true)
    }

    const handleDeleteItem = (itemId: string) => {
        if (confirm('Are you sure you want to delete this item?')) {
            deleteItem(itemId)
        }
    }

    const handleCloseModal = () => {
        setItemModalOpen(false)
        setEditingItem(null)
        setItemModalCategoryId("")
    }

    const handleStatusChange = (itemId: string, status: ItemStatus) => {
        updateItem(itemId, { status })
    }

    const handleAssignUser = (itemId: string, userId: string | undefined) => {
        updateItem(itemId, { assignedUserId: userId })
    }

    const handleAddItemSimple = (categoryId: string, itemName: string) => {
        if (!itemName.trim()) return

        addItem({
            name: itemName.trim(),
            notes: "",
            categoryId,
            purchaseOptions: [],
            status: "pending"
        })

        setNewItemInputs({ ...newItemInputs, [categoryId]: "" })
    }

    const handleKeyPress = (e: React.KeyboardEvent, categoryId: string) => {
        if (e.key === 'Enter') {
            const itemName = newItemInputs[categoryId] || ""
            handleAddItemSimple(categoryId, itemName)
        }
    }

    const toggleViewMode = () => {
        setViewMode(viewMode === 'card' ? 'simple' : 'card')
    }

    return (
        <SidebarProvider>
            <SidebarLeft />
            <SidebarInset className="relative">
                <header className="bg-background sticky top-0 flex h-14 shrink-0 items-center gap-2">
                    <div className="flex flex-1 items-center gap-2 px-3">
                        <SidebarTrigger />
                        <Separator
                            orientation="vertical"
                            className="mr-2 data-[orientation=vertical]:h-4"
                        />
                        <div className="flex items-center gap-2">
                            <span className="text-xl">{activeBucket?.emoji || "🛍️"}</span>
                            <h1 className="font-semibold">{activeBucket?.name || "Shopping List"}</h1>
                        </div>
                    </div>
                    <div className="flex items-center gap-2 px-3">
                        {/* Large desktop view - both buttons with text */}
                        <div className="hidden xl:flex items-center gap-2">
                            <Button
                                variant={viewMode === 'card' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('card')}
                                className="gap-2"
                            >
                                <Grid3X3 className="h-4 w-4" />
                                Card View
                            </Button>
                            <Button
                                variant={viewMode === 'simple' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('simple')}
                                className="gap-2"
                            >
                                <List className="h-4 w-4" />
                                Simple View
                            </Button>
                        </div>

                        {/* Medium and mobile view - icon only buttons */}
                        <div className="xl:hidden flex items-center gap-2">
                            <Button
                                variant={viewMode === 'card' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('card')}
                                className="p-2"
                            >
                                <Grid3X3 className="h-4 w-4" />
                            </Button>
                            <Button
                                variant={viewMode === 'simple' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('simple')}
                                className="p-2"
                            >
                                <List className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </header>
                <div className="flex flex-1 flex-col gap-8 p-6 pb-20 lg:pb-6">
                    {!activeBucket || activeCategories.length === 0 ? (
                        <div className="flex items-center justify-center h-96">
                            <div className="text-center text-muted-foreground">
                                <div className="text-6xl mb-4">🛍️</div>
                                <h2 className="text-xl font-semibold mb-2">
                                    {!activeBucket ? "No bucket selected" : "No categories yet"}
                                </h2>
                                <p>
                                    {!activeBucket
                                        ? "Select a bucket from the sidebar to get started"
                                        : "Start organizing your shopping list by adding categories"
                                    }
                                </p>
                            </div>
                        </div>
                    ) : viewMode === 'card' ? (
                        activeCategories.map((category) => (
                            <CategorySection
                                key={category.id}
                                category={category}
                                users={users}
                                onAddItem={handleAddItem}
                                onEditItem={handleEditItem}
                                onDeleteItem={handleDeleteItem}
                                onStatusChange={handleStatusChange}
                                onAssignUser={handleAssignUser}
                            />
                        ))
                    ) : (
                        // Simple view - like normal shopping list
                        <div className="space-y-6">
                            {activeCategories.map((category) => (
                                <div key={category.id} className="space-y-3">
                                    <div className="flex items-center gap-2 border-b pb-2">
                                        <h3 className="font-medium text-lg">{category.name}</h3>
                                        <Badge variant="outline" className="text-xs">
                                            {category.items?.length || 0} items
                                        </Badge>
                                    </div>

                                    <div className="space-y-2">
                                        {category.items?.map((item) => {
                                            const price = item.purchaseOptions && item.purchaseOptions.length > 0
                                                ? item.purchaseOptions[0].price
                                                : null

                                            return (
                                                <div key={item.id} className="flex items-center gap-3 py-1">
                                                    <button
                                                        onClick={() => handleStatusChange(item.id,
                                                            item.status === 'bought' ? 'pending' : 'bought'
                                                        )}
                                                        className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${item.status === 'bought'
                                                            ? 'bg-green-500 border-green-500 text-white'
                                                            : 'border-gray-300 hover:border-green-400'
                                                            }`}
                                                    >
                                                        {item.status === 'bought' && <Check className="h-3 w-3" />}
                                                    </button>

                                                    <span
                                                        className={`flex-1 cursor-pointer hover:text-blue-600 transition-colors ${item.status === 'bought' ? 'line-through text-muted-foreground' : ''
                                                            }`}
                                                        onClick={() => handleEditItem(item)}
                                                    >
                                                        {item.name}
                                                    </span>

                                                    {price && (
                                                        <span className="text-sm font-medium text-muted-foreground">
                                                            ${price.toFixed(2)}
                                                        </span>
                                                    )}
                                                </div>
                                            )
                                        })}

                                        <div className="flex items-center gap-2 pt-2">
                                            <Input
                                                placeholder="Add new item..."
                                                value={newItemInputs[category.id] || ""}
                                                onChange={(e) => setNewItemInputs({
                                                    ...newItemInputs,
                                                    [category.id]: e.target.value
                                                })}
                                                onKeyPress={(e) => handleKeyPress(e, category.id)}
                                                className="flex-1"
                                            />
                                            <Button
                                                size="sm"
                                                onClick={() => handleAddItemSimple(category.id, newItemInputs[category.id] || "")}
                                                disabled={!newItemInputs[category.id]?.trim()}
                                            >
                                                <Plus className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
                <MobileTotalsBar
                    categories={activeCategories}
                    currentCategoryId={activeCategoryId || undefined}
                    budget={activeBucket?.budget}
                />
            </SidebarInset>
            <SidebarRight
                categories={activeCategories}
                currentCategoryId={activeCategoryId || undefined}
                budget={activeBucket?.budget}
            />

            <ItemModal
                isOpen={itemModalOpen}
                onClose={handleCloseModal}
                onSave={addItem}
                onUpdate={updateItem}
                categoryId={itemModalCategoryId}
                users={users}
                editingItem={editingItem}
            />
        </SidebarProvider>
    )
}
