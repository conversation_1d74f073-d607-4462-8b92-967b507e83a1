# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

The project uses Turbo for monorepo orchestration with <PERSON><PERSON> as the package manager:

- `bun install` - Install dependencies for all workspaces
- `bun run dev` - Run all workspaces in development mode
- `bun run dev:client` - Run only the React client (Vite dev server)
- `bun run dev:server` - Run only the Hono backend server
- `bun run build` - Build all workspaces
- `bun run build:client` - Build only the React frontend
- `bun run build:server` - Build only the Hono backend
- `bun run lint` - Lint all workspaces
- `bun run type-check` - Type check all workspaces
- `bun run test` - Run tests across all workspaces

The postinstall script automatically builds shared and server packages after dependency installation.

## Project Overview: Shopping List Management System

This is a comprehensive shopping list management application built as part of the "Urusan Financial" suite. The application provides advanced features for organizing shopping items with cost analysis, progress tracking, and collaborative features.

## Architecture Overview

This is a full-stack TypeScript monorepo with three main workspaces:

### 1. Shared Package (`/shared`)
- Contains shared TypeScript types and utilities
- Exports types through `src/index.ts` that are used by both client and server
- Must be built before other packages can use it
- Core types: `Bucket`, `Category`, `Item`, `User`, `PurchaseOption`, `Application`

### 2. Server Package (`/server`)
- Hono-based REST API server
- Uses Bun runtime
- Imports shared types from the shared package via `shared/dist`
- Entry point: `src/index.ts`
- Exports Hono client types for type-safe client consumption

### 3. Client Package (`/client`)
- React + Vite frontend application
- Uses Tailwind CSS with shadcn/ui components
- Imports shared types and uses Hono client (`hcWithType`) for type-safe API calls
- Server URL configurable via `VITE_SERVER_URL` environment variable

## Application Features

### Core Data Model
- **Applications**: Multi-app switcher (Shopping List, Budget Planning, Debt Management)
- **Buckets**: Top-level containers (e.g., "Move to New Home", "Travel to Dubai")
- **Categories**: Second-level organization within buckets (e.g., "Furniture", "Electronics")
- **Items**: Individual shopping items with multiple purchase options
- **Users**: For assignment and collaboration features

### Key Features Implemented

#### Phase 1: Core Structure ✅
- Hierarchical navigation (Applications → Buckets → Categories → Items)
- Basic CRUD operations for items
- localStorage persistence
- Responsive sidebar layout

#### Phase 2: Item Management ✅
- Comprehensive ItemModal with form validation
- Multiple purchase options per item with pricing
- Status tracking (Pending → In Cart → Bought)
- User assignment with color-coded indicators
- Enhanced ItemCard with dropdown actions

#### Phase 3: Advanced Features ✅
- **Cost Calculations & Budgeting**: Advanced budget tracking with progress bars, color-coded status, remaining/exceeded calculations
- **Price Comparison**: Intelligent comparison with "Best Price" badges, savings calculations, side-by-side option display
- **Progress Analytics**: Dual progress tracking (completion % and shopping %), spending progress, most active category identification
- **Complete CRUD Operations**: Full bucket and category management with modals, edit/delete confirmations
- **Dual View Modes**: Card view (rich detailed) and Simple view (minimalist shopping list with checkboxes)

## Component Architecture

### Main Layout Components
- `App.tsx` - Main dashboard with view mode toggle
- `SidebarLeft` - Navigation, buckets, and categories
- `SidebarRight` - Cost analysis and progress tracking (320px width)

### Key Components
- `ApplicationSwitcher` - Multi-app navigation
- `BucketList` - Bucket management with "Create a Bucket" as first item
- `CategoryNavigation` - Category management with "Add Category" as first item
- `ItemCard` - Rich item display with price comparison
- `CategorySection` - Groups items by category
- `ItemModal` - Comprehensive item creation/editing
- `BucketModal` - Bucket creation/editing with emoji selection
- `CategoryModal` - Category creation/editing

### Analytics Components
- `CostSummaryFirst` - Budget tracking, current category, price analysis
- `ProgressTracker` - Advanced progress analytics with spending tracking
- `CostSummaryLast` - Bucket overview and quick stats

### State Management
- `ShoppingContext` - Centralized state with React Context
- All CRUD operations with optimistic updates
- Automatic localStorage persistence
- Type-safe operations throughout

## UI/UX Design Patterns

### Navigation Hierarchy
```
Main Navigation: Search, Ask AI, Home, Inbox
Buckets: [Create a Bucket], Move to New Home, Travel to Dubai...
Categories: [Add Category], Furniture, Electronics...
```

### View Modes
- **Card View**: Rich cards with full details, price comparison, user assignment
- **Simple View**: Minimalist checklist with inline adding, Enter key support

### Right Sidebar Layout (320px)
1. Budget Tracking (if budget set)
2. Current Category (if selected)
3. Price Analysis (best/worst case scenarios)
4. Progress Analytics (completion, shopping progress)
5. Bucket Overview (category totals)
6. Quick Stats (counts and summaries)

## Type Safety and RPC Pattern

The project implements end-to-end type safety using Hono's RPC client:
- Server exports Hono app that can be consumed as a typed client
- Client imports `hcWithType` from server package for fully typed API calls
- No need for manual API type definitions - types flow automatically from server to client

## Key Dependencies

### Core Framework
- **Bun**: Runtime and package manager
- **Turbo**: Monorepo build orchestration and caching
- **Hono**: Backend framework with built-in RPC client generation
- **Vite**: Frontend build tool
- **React**: Frontend framework
- **TypeScript**: End-to-end type safety

### UI Components
- **Tailwind CSS**: Utility-first styling
- **shadcn/ui**: High-quality React components
- **Radix UI**: Accessible component primitives
- **Lucide React**: Icon library

### UI Components Used
- Dialog, Select, Textarea, Input, Button, Badge, Card, Progress
- Sidebar, Dropdown Menu, Collapsible, Tooltip, Separator
- Calendar, Avatar, Popover, Sheet, Skeleton, Label

## Development Workflow

1. Make changes to shared types in `/shared/src/types/`
2. Run `cd shared && bun run build` to rebuild types
3. Server and client automatically get updated types
4. Use `bun run dev` to run all services with hot reload

## Data Persistence

- **localStorage**: All data automatically saved on changes
- **Optimistic Updates**: Immediate UI updates with background persistence
- **Migration Ready**: Data structure designed for future API integration

## Mobile Responsiveness

- **Responsive Sidebar**: Collapsible on mobile devices
- **Touch-Friendly**: Proper touch targets and spacing
- **Future Mobile Features**: Bottom drawer for totals access (Phase 4)

## Current Status

All Phase 1-3 features are fully implemented and tested:
- ✅ Core hierarchical structure
- ✅ Complete item management
- ✅ Advanced cost analysis and budgeting
- ✅ Price comparison with savings calculations
- ✅ Comprehensive progress analytics
- ✅ Full CRUD operations for all entities
- ✅ Dual view modes (Card/Simple)

**Next Phase**: Mobile optimization and visual polish (Phase 4)