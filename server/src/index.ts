import { Hono } from "hono";
import { cors } from "hono/cors";
import { sql } from "drizzle-orm";
import type { LegacyApiResponse } from "shared/dist";
import { createDbConnection } from "./db/connection";
import { runMigrations } from "./db/migrate";
import { apiRoutes } from "./routes/api";

const db = createDbConnection();

export const app = new Hono()

	.use(cors())

	.get("/", (c) => {
		return c.text("Hello Hono!");
	})

	.get("/docs", (c) => {
		return c.html(`
		<!DOCTYPE html>
		<html>
		<head>
			<title>Urusan Financial API Documentation</title>
			<meta charset="utf-8" />
			<meta name="viewport" content="width=device-width, initial-scale=1" />
		</head>
		<body>
			<script id="api-reference" data-url="/openapi.json"></script>
			<script src="https://cdn.jsdelivr.net/npm/@scalar/api-reference"></script>
		</body>
		</html>
	`);
	})

	.get("/openapi.json", (c) => {
		return c.json({
			openapi: "3.0.0",
			info: {
				title: "Urusan Financial API",
				version: "1.0.0",
				description: "Multi-player shopping list API for collaborative shopping management"
			},
			servers: [
				{
					url: "http://localhost:3000",
					description: "Development server"
				}
			],
			paths: {
				"/": {
					get: {
						summary: "Root endpoint",
						responses: {
							"200": {
								description: "Success",
								content: {
									"text/plain": {
										schema: {
											type: "string",
											example: "Hello Hono!"
										}
									}
								}
							}
						}
					}
				},
				"/hello": {
					get: {
						summary: "Hello endpoint",
						responses: {
							"200": {
								description: "Success response",
								content: {
									"application/json": {
										schema: {
											type: "object",
											properties: {
												message: { type: "string" },
												success: { type: "boolean" }
											}
										}
									}
								}
							}
						}
					}
				},
				"/health": {
					get: {
						summary: "Health check endpoint",
						responses: {
							"200": {
								description: "Service is healthy"
							},
							"503": {
								description: "Service is unhealthy"
							}
						}
					}
				}
			}
		});
	})

	.route("/api", apiRoutes)

	.get("/hello", async (c) => {
		const data: LegacyApiResponse = {
			message: "Hello BHVR!",
			success: true,
		};

		return c.json(data, { status: 200 });
	})

	.get("/health", async (c) => {
		try {
			// Test database connection with a simple query
			await db.run(sql`SELECT 1`);

			return c.json({
				status: "healthy",
				timestamp: new Date().toISOString(),
				services: {
					web: "ok",
					database: "ok"
				}
			});
		} catch (error) {
			return c.json({
				status: "unhealthy",
				timestamp: new Date().toISOString(),
				services: {
					web: "ok",
					database: "error"
				},
				error: error instanceof Error ? error.message : "Unknown database error"
			}, 503);
		}
	})

	.get("/health/db", async (c) => {
		try {
			// More detailed database health check
			const walCheck = await db.run(sql`PRAGMA journal_mode`);

			return c.json({
				status: "healthy",
				database: {
					connection: "ok",
					wal_mode: "enabled",
					timestamp: new Date().toISOString()
				}
			});
		} catch (error) {
			return c.json({
				status: "unhealthy",
				database: {
					connection: "error",
					error: error instanceof Error ? error.message : "Unknown error"
				}
			}, 503);
		}
	});

export default app;