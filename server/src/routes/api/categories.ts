import { Hono } from "hono";
import type { Category, SuccessResponse, ErrorResponse } from "shared/dist";

export const categoryRoutes = new Hono()

// List categories (mounted under /buckets/:bucketId/categories)
.get("/", async (c) => {
	// TODO: Implement list categories logic
	const response: SuccessResponse<Category[]> = {
		success: true,
		data: [
			{
				id: "category-1",
				name: "Vegetables",
				bucketId: "bucket-1",
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString()
			}
		],
		timestamp: new Date().toISOString()
	};
	
	return c.json(response);
})

// Create category (mounted under /buckets/:bucketId/categories)
.post("/", async (c) => {
	// TODO: Implement create category logic
	const response: SuccessResponse<Category> = {
		success: true,
		data: {
			id: "category-2",
			name: "New Category",
			bucketId: "bucket-1",
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString()
		},
		timestamp: new Date().toISOString()
	};
	
	return c.json(response, 201);
})

// Update category
.put("/:id", async (c) => {
	const categoryId = c.req.param("id");
	
	// TODO: Implement update category logic
	const response: SuccessResponse<Category> = {
		success: true,
		data: {
			id: categoryId,
			name: "Updated Category",
			bucketId: "bucket-1",
			updatedAt: new Date().toISOString()
		},
		timestamp: new Date().toISOString()
	};
	
	return c.json(response);
})

// Delete category
.delete("/:id", async (c) => {
	const categoryId = c.req.param("id");
	
	// TODO: Implement delete category logic
	const response: SuccessResponse<{ message: string }> = {
		success: true,
		data: {
			message: `Category ${categoryId} deleted successfully`
		},
		timestamp: new Date().toISOString()
	};
	
	return c.json(response);
});