import { Hono } from "hono";
import { authRoutes } from "./auth";
import { userRoutes } from "./users";
import { bucketRoutes } from "./buckets";
import { categoryRoutes } from "./categories";
import { itemRoutes } from "./items";

export const apiRoutes = new Hono()

// Authentication routes
.route("/auth", authRoutes)

// User management routes
.route("/users", userRoutes)

// Bucket management routes
.route("/buckets", bucketRoutes)

// Category management routes - nested under buckets
.route("/buckets/:bucketId/categories", categoryRoutes)

// Category management routes - direct access
.route("/categories", categoryRoutes)

// Item management routes - nested under categories
.route("/categories/:categoryId/items", itemRoutes)

// Item management routes - direct access
.route("/items", itemRoutes);