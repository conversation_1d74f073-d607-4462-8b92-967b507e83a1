import { migrate } from 'drizzle-orm/bun-sqlite/migrator';
import { createDbConnection } from './connection';

export async function runMigrations(dbPath: string = './dev.db') {
  const db = createDbConnection(dbPath);
  
  console.log('🚀 Running database migrations...');
  
  try {
    await migrate(db, { migrationsFolder: './migrations' });
    console.log('✅ Database migrations completed successfully');
    console.log('📄 WAL mode enabled for better performance');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Run migrations if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runMigrations().catch(console.error);
}

runMigrations().catch(console.error);