import { drizzle } from 'drizzle-orm/bun-sqlite';
import { Database } from 'bun:sqlite';
import * as schema from './schema';

export function createDbConnection(dbPath: string = './dev.db') {
  const sqlite = new Database(dbPath);
  
  // Enable WAL mode for better performance and concurrency
  sqlite.run('PRAGMA journal_mode = WAL');
  sqlite.run('PRAGMA synchronous = NORMAL');
  sqlite.run('PRAGMA cache_size = 1000000');
  sqlite.run('PRAGMA foreign_keys = ON');
  sqlite.run('PRAGMA temp_store = MEMORY');
  
  return drizzle(sqlite, { schema });
}

export type DbConnection = ReturnType<typeof createDbConnection>;