import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

export const users = sqliteTable('users', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  color: text('color').notNull(),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
});

export const buckets = sqliteTable('buckets', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  emoji: text('emoji').notNull(),
  ownerId: text('owner_id').references(() => users.id),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`),
});

export const bucketCollaborators = sqliteTable('bucket_collaborators', {
  bucketId: text('bucket_id').references(() => buckets.id),
  userId: text('user_id').references(() => users.id),
  role: text('role', { enum: ['owner', 'member'] }).default('member'),
  joinedAt: text('joined_at').default(sql`CURRENT_TIMESTAMP`),
});

export const categories = sqliteTable('categories', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  bucketId: text('bucket_id').references(() => buckets.id),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`),
});

export const items = sqliteTable('items', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  notes: text('notes'),
  categoryId: text('category_id').references(() => categories.id),
  status: text('status', { enum: ['pending', 'in-cart', 'bought'] }).default('pending'),
  assignedUserId: text('assigned_user_id').references(() => users.id),
  selectedOptionId: text('selected_option_id'),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`),
});

export const purchaseOptions = sqliteTable('purchase_options', {
  id: text('id').primaryKey(),
  itemId: text('item_id').references(() => items.id),
  storeName: text('store_name').notNull(),
  price: real('price'),
  link: text('link'),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
});

export const changeLog = sqliteTable('change_log', {
  id: integer('id', { mode: 'number' }).primaryKey({ autoIncrement: true }),
  bucketId: text('bucket_id').references(() => buckets.id),
  entityType: text('entity_type', { 
    enum: ['bucket', 'category', 'item', 'purchase_option'] 
  }).notNull(),
  entityId: text('entity_id').notNull(),
  action: text('action', { enum: ['create', 'update', 'delete'] }).notNull(),
  userId: text('user_id').references(() => users.id),
  timestamp: text('timestamp').default(sql`CURRENT_TIMESTAMP`),
  data: text('data'), // JSON payload
});