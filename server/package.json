{"name": "server", "version": "0.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "bun --watch run src/index.ts && tsc --watch", "db:generate": "drizzle-kit generate", "db:migrate": "bun run src/db/migrate.ts", "db:studio": "drizzle-kit studio"}, "dependencies": {"@scalar/hono-api-reference": "^0.9.12", "drizzle-orm": "^0.44.3", "hono": "^4.7.11", "shared": "workspace:*"}, "devDependencies": {"@types/bun": "latest", "drizzle-kit": "^0.31.4", "tsx": "^4.20.3"}}