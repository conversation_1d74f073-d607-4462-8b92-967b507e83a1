{"version": "6", "dialect": "sqlite", "id": "d000e7bf-2233-4b2d-b97d-5309057f3871", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"bucket_collaborators": {"name": "bucket_collaborators", "columns": {"bucket_id": {"name": "bucket_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'member'"}, "joined_at": {"name": "joined_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"bucket_collaborators_bucket_id_buckets_id_fk": {"name": "bucket_collaborators_bucket_id_buckets_id_fk", "tableFrom": "bucket_collaborators", "tableTo": "buckets", "columnsFrom": ["bucket_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "bucket_collaborators_user_id_users_id_fk": {"name": "bucket_collaborators_user_id_users_id_fk", "tableFrom": "bucket_collaborators", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "buckets": {"name": "buckets", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "emoji": {"name": "emoji", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "owner_id": {"name": "owner_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"buckets_owner_id_users_id_fk": {"name": "buckets_owner_id_users_id_fk", "tableFrom": "buckets", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "categories": {"name": "categories", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "bucket_id": {"name": "bucket_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"categories_bucket_id_buckets_id_fk": {"name": "categories_bucket_id_buckets_id_fk", "tableFrom": "categories", "tableTo": "buckets", "columnsFrom": ["bucket_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "change_log": {"name": "change_log", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "bucket_id": {"name": "bucket_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "entity_type": {"name": "entity_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "entity_id": {"name": "entity_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"change_log_bucket_id_buckets_id_fk": {"name": "change_log_bucket_id_buckets_id_fk", "tableFrom": "change_log", "tableTo": "buckets", "columnsFrom": ["bucket_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "change_log_user_id_users_id_fk": {"name": "change_log_user_id_users_id_fk", "tableFrom": "change_log", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "items": {"name": "items", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'pending'"}, "assigned_user_id": {"name": "assigned_user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "selected_option_id": {"name": "selected_option_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"items_category_id_categories_id_fk": {"name": "items_category_id_categories_id_fk", "tableFrom": "items", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "items_assigned_user_id_users_id_fk": {"name": "items_assigned_user_id_users_id_fk", "tableFrom": "items", "tableTo": "users", "columnsFrom": ["assigned_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "purchase_options": {"name": "purchase_options", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "item_id": {"name": "item_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "store_name": {"name": "store_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price": {"name": "price", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "link": {"name": "link", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"purchase_options_item_id_items_id_fk": {"name": "purchase_options_item_id_items_id_fk", "tableFrom": "purchase_options", "tableTo": "items", "columnsFrom": ["item_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}