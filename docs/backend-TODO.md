# Backend Implementation TODO - Multi-Player Shopping List

## Overview
Transform the current hello-world Hono backend into a robust real-time multi-player shopping list API designed for Cloudflare Workers. The backend must support concurrent collaboration between multiple users (primary use case: husband and wife) with real-time synchronization.

## Architecture Goals
- **Platform**: Cloudflare Workers for global edge deployment
- **Database**: Cloudflare D1 (SQLite) for production, with Drizzle ORM for flexibility
- **ORM**: Drizzle ORM for type-safe database operations and easy switching
- **Real-time**: WebSockets via Cloudflare Durable Objects
- **Auth**: Simple session-based authentication
- **API**: RESTful with real-time updates
- **Type Safety**: End-to-end TypeScript with Hono RPC client

## Phase 1: Core Infrastructure Setup

### 1. Cloudflare Workers Configuration
- [ ] Configure `wrangler.toml` for Cloudflare Workers deployment
- [ ] Set up Cloudflare D1 database binding
- [ ] Configure Durable Objects for WebSocket connections
- [ ] Set up environment variables and secrets management
- [ ] Configure custom domains and routing

### 1.5. Drizzle ORM Setup
- [ ] Install Drizzle ORM and related packages:
  ```bash
  bun add drizzle-orm
  bun add -D drizzle-kit @types/better-sqlite3
  ```
- [ ] Configure Drizzle for multiple database adapters:
  - Cloudflare D1 adapter for production
  - Better-SQLite3 for local development
  - PostgreSQL adapter for future scaling
- [ ] Set up `drizzle.config.ts` with environment-based configuration
- [ ] Create database connection factory for different environments

### 2. Database Schema Design with Drizzle
- [ ] Define Drizzle schema in TypeScript with proper types:
  ```typescript
  // src/db/schema.ts
  import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';
  import { sql } from 'drizzle-orm';

  export const users = sqliteTable('users', {
    id: text('id').primaryKey(),
    name: text('name').notNull(),
    color: text('color').notNull(),
    createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  });

  export const buckets = sqliteTable('buckets', {
    id: text('id').primaryKey(),
    name: text('name').notNull(),
    emoji: text('emoji').notNull(),
    ownerId: text('owner_id').references(() => users.id),
    createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
    updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`),
  });

  export const bucketCollaborators = sqliteTable('bucket_collaborators', {
    bucketId: text('bucket_id').references(() => buckets.id),
    userId: text('user_id').references(() => users.id),
    role: text('role', { enum: ['owner', 'member'] }).default('member'),
    joinedAt: text('joined_at').default(sql`CURRENT_TIMESTAMP`),
  });

  export const categories = sqliteTable('categories', {
    id: text('id').primaryKey(),
    name: text('name').notNull(),
    bucketId: text('bucket_id').references(() => buckets.id),
    createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
    updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`),
  });

  export const items = sqliteTable('items', {
    id: text('id').primaryKey(),
    name: text('name').notNull(),
    notes: text('notes'),
    categoryId: text('category_id').references(() => categories.id),
    status: text('status', { enum: ['pending', 'in-cart', 'bought'] }).default('pending'),
    assignedUserId: text('assigned_user_id').references(() => users.id),
    selectedOptionId: text('selected_option_id'),
    createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
    updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`),
  });

  export const purchaseOptions = sqliteTable('purchase_options', {
    id: text('id').primaryKey(),
    itemId: text('item_id').references(() => items.id),
    storeName: text('store_name').notNull(),
    price: real('price'),
    link: text('link'),
    createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  });

  export const changeLog = sqliteTable('change_log', {
    id: integer('id', { mode: 'number' }).primaryKey({ autoIncrement: true }),
    bucketId: text('bucket_id').references(() => buckets.id),
    entityType: text('entity_type', { 
      enum: ['bucket', 'category', 'item', 'purchase_option'] 
    }).notNull(),
    entityId: text('entity_id').notNull(),
    action: text('action', { enum: ['create', 'update', 'delete'] }).notNull(),
    userId: text('user_id').references(() => users.id),
    timestamp: text('timestamp').default(sql`CURRENT_TIMESTAMP`),
    data: text('data'), // JSON payload
  });
  ```

- [ ] Create database connection factory for different environments:
  ```typescript
  // src/db/connection.ts
  import { drizzle } from 'drizzle-orm/d1';
  import { drizzle as drizzleBetter } from 'drizzle-orm/better-sqlite3';
  import { drizzle as drizzlePg } from 'drizzle-orm/postgres-js';
  import Database from 'better-sqlite3';
  import postgres from 'postgres';
  import * as schema from './schema';

  export function createDbConnection(env: 'development' | 'production' | 'postgres', binding?: any) {
    switch (env) {
      case 'development':
        const sqlite = new Database('dev.db');
        return drizzleBetter(sqlite, { schema });
      
      case 'production':
        return drizzle(binding, { schema });
      
      case 'postgres':
        const client = postgres(process.env.DATABASE_URL!);
        return drizzlePg(client, { schema });
      
      default:
        throw new Error(`Unknown environment: ${env}`);
    }
  }
  ```

- [ ] Set up Drizzle migrations with `drizzle-kit`
- [ ] Create migration scripts for different database types
- [ ] Add database seeding utilities for development

### 3. Authentication System
- [ ] Implement simple session-based authentication
- [ ] Create user registration/login endpoints
- [ ] Session management with secure cookies
- [ ] User profile management
- [ ] Password hashing with bcrypt or Argon2

## Phase 2: Core API Development

### 1. RESTful API Endpoints

#### User Management
- [ ] `POST /api/auth/register` - User registration
- [ ] `POST /api/auth/login` - User login
- [ ] `POST /api/auth/logout` - User logout
- [ ] `GET /api/auth/me` - Get current user
- [ ] `PUT /api/users/:id` - Update user profile

#### Bucket Management
- [ ] `GET /api/buckets` - List user's buckets
- [ ] `POST /api/buckets` - Create new bucket
- [ ] `GET /api/buckets/:id` - Get bucket details
- [ ] `PUT /api/buckets/:id` - Update bucket
- [ ] `DELETE /api/buckets/:id` - Delete bucket
- [ ] `POST /api/buckets/:id/collaborators` - Add collaborator
- [ ] `DELETE /api/buckets/:id/collaborators/:userId` - Remove collaborator

#### Category Management
- [ ] `GET /api/buckets/:bucketId/categories` - List categories
- [ ] `POST /api/buckets/:bucketId/categories` - Create category
- [ ] `PUT /api/categories/:id` - Update category
- [ ] `DELETE /api/categories/:id` - Delete category

#### Item Management
- [ ] `GET /api/categories/:categoryId/items` - List items
- [ ] `POST /api/categories/:categoryId/items` - Create item
- [ ] `GET /api/items/:id` - Get item details
- [ ] `PUT /api/items/:id` - Update item
- [ ] `DELETE /api/items/:id` - Delete item
- [ ] `PUT /api/items/:id/status` - Update item status
- [ ] `PUT /api/items/:id/assign` - Assign item to user

#### Purchase Options Management
- [ ] `POST /api/items/:itemId/options` - Add purchase option
- [ ] `PUT /api/options/:id` - Update purchase option
- [ ] `DELETE /api/options/:id` - Delete purchase option
- [ ] `PUT /api/items/:id/select-option` - Select preferred option

### 2. Business Logic Implementation with Drizzle
- [ ] Create repository pattern with Drizzle queries:
  ```typescript
  // src/repositories/BucketRepository.ts
  import { eq, and } from 'drizzle-orm';
  import { buckets, bucketCollaborators } from '../db/schema';
  
  export class BucketRepository {
    constructor(private db: DrizzleDB) {}
    
    async findUserBuckets(userId: string) {
      return await this.db
        .select()
        .from(buckets)
        .leftJoin(bucketCollaborators, eq(buckets.id, bucketCollaborators.bucketId))
        .where(
          or(
            eq(buckets.ownerId, userId),
            eq(bucketCollaborators.userId, userId)
          )
        );
    }
    
    async createBucket(data: InsertBucket) {
      return await this.db.insert(buckets).values(data).returning();
    }
  }
  ```
- [ ] Implement CRUD operations with proper validation using Drizzle
- [ ] Add data sanitization and input validation with Zod schemas
- [ ] Implement proper error handling and status codes
- [ ] Add request rate limiting
- [ ] Implement soft deletes for data recovery
- [ ] Add audit logging for all changes using changeLog table
- [ ] Create type-safe query builders for complex operations

### 3. Authorization & Permissions
- [ ] Implement bucket-level access control
- [ ] Ensure users can only access their buckets
- [ ] Add role-based permissions (owner vs member)
- [ ] Validate collaborator access on all operations
- [ ] Implement invitation system for adding collaborators

## Phase 3: Real-Time Synchronization

### 1. WebSocket Implementation with Durable Objects
- [ ] Create Durable Object class for bucket sessions
- [ ] Implement WebSocket connection management
- [ ] Handle user join/leave events
- [ ] Implement connection heartbeat and reconnection
- [ ] Add proper error handling and cleanup

### 2. Real-Time Event System
- [ ] Define event types for all data changes:
  ```typescript
  type RealtimeEvent = 
    | { type: 'bucket_updated', payload: Bucket }
    | { type: 'category_created', payload: Category }
    | { type: 'category_updated', payload: Category }
    | { type: 'category_deleted', payload: { id: string } }
    | { type: 'item_created', payload: Item }
    | { type: 'item_updated', payload: Item }
    | { type: 'item_deleted', payload: { id: string } }
    | { type: 'item_status_changed', payload: { id: string, status: ItemStatus } }
    | { type: 'item_assigned', payload: { id: string, userId: string } }
    | { type: 'purchase_option_added', payload: PurchaseOption }
    | { type: 'user_joined', payload: { userId: string, userName: string } }
    | { type: 'user_left', payload: { userId: string } }
  ```
- [ ] Implement event broadcasting to all bucket collaborators
- [ ] Add event persistence for offline users
- [ ] Implement event deduplication

### 3. Conflict Resolution
- [ ] Implement optimistic locking for concurrent updates
- [ ] Add timestamp-based conflict resolution
- [ ] Handle concurrent item status changes
- [ ] Implement "last writer wins" with user attribution
- [ ] Add change log for debugging conflicts

### 4. Offline Support & Sync
- [ ] Implement change queuing for offline clients
- [ ] Add sync mechanism for reconnected clients
- [ ] Handle partial sync scenarios
- [ ] Implement data versioning for sync conflicts

## Phase 4: Advanced Features

### 1. Cost Calculation Engine
- [ ] Implement real-time cost calculations
- [ ] Add support for different currencies
- [ ] Calculate totals by status (pending/in-cart/bought)
- [ ] Implement budget tracking and alerts
- [ ] Add price history tracking

### 2. Analytics & Reporting
- [ ] Track shopping progress metrics
- [ ] Generate spending reports
- [ ] Add completion time analytics
- [ ] Implement user activity tracking
- [ ] Create shareable summary reports

### 3. Notification System
- [ ] Email notifications for bucket invitations
- [ ] Push notifications for status changes
- [ ] Daily/weekly shopping summaries
- [ ] Budget threshold alerts
- [ ] Item assignment notifications

### 4. Data Export/Import
- [ ] CSV export functionality
- [ ] JSON backup/restore
- [ ] Integration with shopping apps
- [ ] Bulk import from common formats
- [ ] Data migration utilities

## Phase 5: Performance & Production Readiness

### 1. Caching Strategy
- [ ] Implement Cloudflare Workers KV for session caching
- [ ] Add response caching for read-heavy operations
- [ ] Implement smart cache invalidation
- [ ] Add edge caching for static data
- [ ] Use browser cache headers appropriately

### 2. Monitoring & Observability
- [ ] Add comprehensive logging with structured data
- [ ] Implement error tracking and alerting
- [ ] Add performance monitoring
- [ ] Create health check endpoints
- [ ] Add metrics collection for analytics

### 3. Security Hardening
- [ ] Implement CSRF protection
- [ ] Add rate limiting per user/bucket
- [ ] Sanitize all user inputs
- [ ] Add SQL injection protection
- [ ] Implement proper CORS policies
- [ ] Add security headers

### 4. Testing Strategy
- [ ] Unit tests for all business logic
- [ ] Integration tests for API endpoints
- [ ] WebSocket connection testing
- [ ] Load testing for concurrent users
- [ ] Database migration testing
- [ ] End-to-end testing scenarios

## Phase 6: Deployment & DevOps

### 1. CI/CD Pipeline
- [ ] Set up GitHub Actions for Cloudflare Workers
- [ ] Implement automated testing in CI
- [ ] Add Drizzle migration automation for different environments
- [ ] Set up staging environment with separate D1 database
- [ ] Implement blue-green deployments
- [ ] Add database schema drift detection

### 2. Environment Management
- [ ] Configure development/staging/production environments
- [ ] Set up database replication/backups
- [ ] Implement secrets management
- [ ] Add environment-specific configurations
- [ ] Set up monitoring and alerting

### 3. Documentation
- [ ] API documentation with OpenAPI/Swagger
- [ ] WebSocket event documentation
- [ ] Database schema documentation
- [ ] Deployment guide
- [ ] Development setup instructions

## Technical Specifications

### API Response Format
```typescript
// Success response
type SuccessResponse<T> = {
  success: true;
  data: T;
  timestamp: string;
}

// Error response
type ErrorResponse = {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

### WebSocket Message Format
```typescript
type WebSocketMessage = {
  id: string; // Message ID for acknowledgment
  type: 'event' | 'ack' | 'error';
  bucketId: string;
  userId: string;
  timestamp: string;
  payload: RealtimeEvent | AckMessage | ErrorMessage;
}
```

### Performance Targets
- **API Response Time**: <100ms for 95th percentile
- **WebSocket Latency**: <50ms for real-time updates
- **Concurrent Users**: Support 100+ concurrent users per bucket
- **Database Operations**: <50ms for 95th percentile queries
- **Uptime**: 99.9% availability target

### Security Requirements
- **Authentication**: Session-based with secure cookies
- **Authorization**: Bucket-level access control
- **Data Encryption**: TLS 1.3 for all communications
- **Input Validation**: Comprehensive sanitization
- **Rate Limiting**: 1000 requests per minute per user
- **Data Privacy**: GDPR compliance considerations

## Migration Strategy

### From Current State
1. **Drizzle Setup**: Install and configure Drizzle ORM with multiple adapters
2. **Database Setup**: Create D1 database and run Drizzle migrations  
3. **Local Development**: Set up better-sqlite3 for local development
4. **API Migration**: Gradually replace hello-world endpoints with Drizzle queries
5. **Type Updates**: Extend shared types for new features using Drizzle inferred types
6. **Client Integration**: Update frontend to use new API
7. **Real-time Integration**: Add WebSocket connections
8. **Database Flexibility**: Test switching between SQLite and PostgreSQL
9. **Testing**: Comprehensive testing before production
10. **Deployment**: Staged rollout with monitoring

### Backwards Compatibility
- [ ] Maintain existing API contracts during transition
- [ ] Version API endpoints appropriately
- [ ] Provide migration guides for breaking changes
- [ ] Support both old and new data formats temporarily

## Drizzle Configuration Examples

### drizzle.config.ts
```typescript
import type { Config } from 'drizzle-kit';

export default {
  schema: './src/db/schema.ts',
  out: './migrations',
  driver: 'd1',
  dbCredentials: {
    wranglerConfigPath: './wrangler.toml',
    dbName: 'shopping-list-db',
  },
  // Alternative configs for different environments
  ...(process.env.NODE_ENV === 'development' && {
    driver: 'better-sqlite',
    dbCredentials: { url: './dev.db' },
  }),
  ...(process.env.NODE_ENV === 'postgres' && {
    driver: 'pg',
    dbCredentials: { 
      connectionString: process.env.DATABASE_URL!,
    },
  }),
} satisfies Config;
```

### Package.json Scripts
```json
{
  "scripts": {
    "db:generate": "drizzle-kit generate:sqlite",
    "db:migrate:dev": "drizzle-kit migrate --config=drizzle.dev.config.ts", 
    "db:migrate:prod": "wrangler d1 migrations apply shopping-list-db",
    "db:push": "drizzle-kit push:sqlite",
    "db:studio": "drizzle-kit studio",
    "db:seed": "bun run src/db/seed.ts"
  }
}
```

This comprehensive backend implementation will transform the simple Hono hello-world server into a production-ready, real-time collaborative shopping list application with **Drizzle ORM for database flexibility**, perfectly suited for couples and small groups to manage their shopping needs together. The use of Drizzle allows easy switching between SQLite (D1) for production, local SQLite for development, and PostgreSQL for future scaling needs.