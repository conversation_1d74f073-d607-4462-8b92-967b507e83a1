# Shopping List Management Features

## Layout Structure

The application uses a **three-panel layout** that adapts to different screen sizes:

### Desktop Layout (3-Panel System)
**Left Sidebar - Navigation Panel**
- **Purpose**: Project and category navigation
- **Content**: 
  - "Shopping Lists" header with quick add bucket button
  - Collapsible bucket list with emoji icons and names
  - Expandable categories within each bucket showing item counts
  - Quick actions (edit/delete) for buckets and categories via dropdown menus
  - "Add Category" button for the active bucket
- **Behavior**: Collapsible sidebar that can be minimized to save space

**Center Panel - Main Content Area**
- **Purpose**: Primary workspace for viewing and managing items
- **Content**:
  - Header showing current bucket name and icon with sidebar toggle
  - Category sections with item counts and "Add Item" buttons
  - Item cards displaying key information (name, notes, status, price, assigned user)
  - Hover actions for editing/deleting items
- **Behavior**: Scrollable content area that adapts to available space

**Right Sidebar - Summary & Analytics Panel**
- **Purpose**: Cost calculations and progress tracking
- **Content**:
  - Current category totals (selected, minimum, maximum prices)
  - Bucket overview with per-category totals and grand total
  - Price range summaries
  - Progress statistics (pending/in-cart/bought item counts)
  - Visual progress bar showing completion percentage
- **Behavior**: Fixed sidebar visible only on large screens (lg+), automatically hidden on smaller screens

### Mobile Layout (Responsive Adaptation)
**Mobile Navigation Bar**
- **Purpose**: Bucket switching and main navigation on mobile
- **Content**: 
  - Hamburger menu button to access bucket list
  - "Shopping List" title
  - Slide-out overlay menu for bucket selection and management
- **Behavior**: Replaces left sidebar functionality on mobile devices

**Mobile Content Area**
- **Purpose**: Same as desktop center panel but optimized for touch
- **Content**: Full-width item cards and categories
- **Behavior**: Stacks vertically with touch-optimized spacing

**Mobile Totals Drawer**
- **Purpose**: Replaces right sidebar functionality on mobile
- **Content**: Same summary information as desktop right sidebar
- **Behavior**: Bottom drawer accessible via pull-up gesture or dedicated button

### Responsive Behavior
- **Breakpoints**: Uses Tailwind's responsive system (md:, lg: prefixes)
- **Sidebar Collapse**: Left sidebar can be collapsed to icon-only mode
- **Mobile Optimization**: Sidebars become overlay panels on smaller screens
- **Touch Targets**: Larger buttons and spacing on mobile for better touch interaction
- **Scroll Behavior**: Smooth scrolling to categories when selected from navigation

## 1. Project Organization (Buckets)
**What it does**: Create separate shopping projects to organize different life events or purposes
**How it works**: 
- Create multiple "buckets" representing different shopping scenarios (e.g., "Move to New Home", "Travel to Dubai")
- Each bucket has a custom name and emoji icon for easy identification
- Switch between buckets to view different shopping projects
- Edit bucket names and icons or delete entire buckets
- All items and categories within a bucket are organized together

## 2. Category Management
**What it does**: Group related items within each bucket for better organization
**How it works**:
- Create categories within each bucket (e.g., "Furniture", "Workspace", "Accommodations", "Clothes")
- Each category shows item count and provides focused view of related items
- Edit category names or delete categories (removes all items within)
- Navigate between categories using sidebar navigation

## 3. Item Management
**What it does**: Track individual shopping items with detailed information and multiple purchasing options
**How it works**:
- Add items with names, notes, and multiple purchase options
- Each item can have unlimited purchase options with different prices and store links
- View items in organized cards showing key information at a glance
- Edit item details, notes, and all purchase options
- Delete items when no longer needed

## 4. Price Comparison
**What it does**: Compare prices across different stores and options for each item
**How it works**:
- Add multiple purchase options for each item (different stores, brands, models)
- Each option includes store/product name, price, and direct link to purchase
- View price ranges when multiple options exist
- Select preferred option to lock in specific price
- Easily add new options or remove existing ones through item modal

## 5. Purchase Status Tracking
**What it does**: Track the progress of each item through the shopping process
**How it works**:
- Three status levels: Pending (not yet decided), In Cart (added to shopping cart), Bought (purchased)
- Visual status badges with color coding (yellow for pending, blue for in-cart, green for bought)
- Change status directly from item detail view
- Filter and understand shopping progress at a glance

## 6. User Assignment
**What it does**: Assign shopping items to different people for coordinated shopping
**How it works**:
- Assign items to specific users (Tegar, Wife, Friend, or leave unassigned)
- Each user has a unique color identifier for easy visual recognition
- View who is responsible for purchasing each item
- Reassign items between users as needed
- Color-coded indicators throughout the interface

## 7. Personal Notes
**What it does**: Add private notes and thoughts about each item
**How it works**:
- Separate from the main item description for personal considerations
- Add notes about preferences, sizing, timing, or other personal factors
- Edit notes directly in the item detail modal
- Notes are saved automatically when you finish typing

## 8. Cost Calculation & Budgeting
**What it does**: Automatically calculate total costs and budget estimates
**How it works**:
- Right sidebar shows total costs for the current category
- Calculates based on selected options (or cheapest option if none selected)
- Shows totals by status: pending items, in-cart items, and bought items
- Mobile users can access totals through a bottom drawer
- Real-time updates as prices and selections change

## 9. Responsive Mobile Interface
**What it does**: Provides full functionality on mobile devices with optimized layout
**How it works**:
- Collapsible sidebars that become slide-out panels on mobile
- Mobile-specific navigation bar for switching between buckets
- Touch-optimized buttons and interactions
- Responsive grid layouts that stack appropriately on small screens
- Totals accessible via bottom drawer on mobile

## 10. Data Persistence
**What it does**: Saves all changes and maintains data across browser sessions
**How it works**:
- All changes are immediately saved to browser's local state
- Data persists when refreshing the page or closing/reopening the browser
- No account creation or cloud sync required
- All data stays on your device for privacy

## 11. Direct Purchase Links
**What it does**: Quick access to purchase items from various online stores
**How it works**:
- Each purchase option includes a direct link to the product page
- Links open in new tabs to avoid losing your place in the shopping list
- Works with any online store (Amazon, local retailers, etc.)
- No need to search for items again when ready to purchase

## 12. Bulk Operations
**What it does**: Efficiently manage multiple items and categories at once
**How it works**:
- Delete entire categories to remove all related items
- Delete buckets to remove entire shopping projects
- Confirmation dialogs prevent accidental deletions
- Edit operations affect all related items automatically

## 13. Visual Organization
**What it does**: Uses visual cues to make information easy to scan and understand
**How it works**:
- Color-coded status badges for quick status identification
- User assignment shown with personalized color indicators
- Emoji icons for buckets make projects easily recognizable
- Hover states and visual feedback for interactive elements
- Clean card-based layout for easy scanning of items